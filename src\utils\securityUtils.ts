/**
 * 安全工具函数
 * 处理敏感操作后的安全清理和状态管理
 */

import { setCachedUserInfo } from "@/services/authService";

/**
 * 清除所有认证信息和用户状态
 * 用于敏感操作后的安全清理（如修改密码、删除账户等）
 */
export async function clearAllAuthData(): Promise<void> {
  try {
    console.log("🔒 开始清除所有认证数据...");

    // 1. 清除localStorage中的认证信息
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_refresh_token");
      localStorage.removeItem("auth_expires");
      console.log("✅ 已清除localStorage认证数据");
    }

    // 2. 清除内存中的用户信息缓存
    setCachedUserInfo(null);
    console.log("✅ 已清除用户信息缓存");

    // 3. 停止token管理器
    try {
      const { tokenManager } = await import("./tokenManager");
      tokenManager.stop();
      console.log("✅ 已停止token管理器");
    } catch (tokenError) {
      console.warn("⚠️ 停止token管理器时出错:", tokenError);
    }

    // 4. 清除请求缓存（如果存在）
    try {
      const { clearRequestCache } = await import("../services/authService");
      if (clearRequestCache) {
        clearRequestCache();
        console.log("✅ 已清除请求缓存");
      }
    } catch {
      // 如果没有clearRequestCache函数，忽略错误
      console.log("ℹ️ 无需清除请求缓存");
    }

    // 5. 触发全局认证状态清除事件
    if (typeof window !== "undefined") {
      window.dispatchEvent(
        new CustomEvent("auth:force-logout", {
          detail: { reason: "security_operation" },
        })
      );
      console.log("✅ 已触发强制登出事件");
    }

    console.log("🔒 认证数据清除完成");
  } catch (error) {
    console.error("❌ 清除认证数据时出错:", error);
    throw error;
  }
}

/**
 * 安全操作后的强制重新登录
 * @param reason 强制登录的原因
 * @param message 显示给用户的消息
 * @param redirectDelay 跳转延迟时间（毫秒）
 */
export async function forceReauthentication(
  reason: string,
  message: string,
  redirectDelay: number = 3000
): Promise<void> {
  try {
    console.log(`🔒 执行强制重新认证，原因: ${reason}`);

    // 1. 清除所有认证数据
    await clearAllAuthData();

    // 2. 显示用户友好的消息
    console.log(`📢 用户消息: ${message}`);

    // 3. 延迟跳转到登录页面
    if (typeof window !== "undefined") {
      setTimeout(() => {
        const loginUrl = `/auth/login?message=${encodeURIComponent(
          reason
        )}&info=${encodeURIComponent(message)}`;
        window.location.href = loginUrl;
      }, redirectDelay);
    }

    console.log(`🔒 将在 ${redirectDelay}ms 后跳转到登录页面`);
  } catch (error) {
    console.error("❌ 强制重新认证时出错:", error);

    // 即使出错也要跳转到登录页面
    if (typeof window !== "undefined") {
      window.location.href = "/auth/login?message=security_error";
    }
  }
}

/**
 * 密码修改后的安全处理
 */
export async function handlePasswordChangeSuccess(): Promise<void> {
  await forceReauthentication(
    "password_changed",
    "密码修改成功，为了您的账户安全，请重新登录",
    3000
  );
}

/**
 * 邮箱修改后的安全处理
 */
export async function handleEmailChangeSuccess(): Promise<void> {
  await forceReauthentication(
    "email_changed",
    "邮箱修改成功，为了您的账户安全，请重新登录",
    3000
  );
}

/**
 * 检查是否需要强制重新认证
 * @param lastSecurityOperation 上次安全操作的时间戳
 * @param maxAge 最大有效期（毫秒）
 */
export function shouldForceReauth(
  lastSecurityOperation: number,
  maxAge: number = 24 * 60 * 60 * 1000
): boolean {
  const now = Date.now();
  return now - lastSecurityOperation > maxAge;
}

/**
 * 记录安全操作时间
 * @param operation 操作类型
 */
export function recordSecurityOperation(operation: string): void {
  if (typeof window !== "undefined") {
    const timestamp = Date.now();
    localStorage.setItem(`security_op_${operation}`, timestamp.toString());
    console.log(
      `🔒 记录安全操作: ${operation} at ${new Date(timestamp).toISOString()}`
    );
  }
}
