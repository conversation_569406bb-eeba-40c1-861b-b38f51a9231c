"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  getCurrentUser,
  isAuthenticated,
  logout as authLogout,
  User,
} from "@/services/authService";
import { useToast } from "@/components/ToastProvider";
import { authStateTracker } from "@/utils/authStateTracker";

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export function useAuth() {
  const router = useRouter();
  const { showToast } = useToast();

  // 修复水合错误：确保服务端和客户端的初始状态一致
  // 在初始化时，无论是否有token，都设置为未认证状态
  // 然后通过useEffect在客户端挂载后检查真实的认证状态
  const [authState, setAuthState] = useState<AuthState>(() => {
    // 在客户端环境下，尝试从localStorage快速获取初始状态
    if (typeof window !== "undefined") {
      const hasToken = !!localStorage.getItem("auth_token");
      const cachedUser = localStorage.getItem("auth_user");

      if (hasToken && cachedUser) {
        try {
          const user = JSON.parse(cachedUser);
          console.log("🔐 useAuth初始化: 从缓存恢复状态", {
            user: user.username,
          });
          return {
            user,
            isLoading: false, // 有缓存数据时不显示加载状态
            isAuthenticated: true,
          };
        } catch (error) {
          console.error("🔐 useAuth初始化: 解析缓存用户信息失败", error);
        }
      }
    }

    // 默认状态（服务端或无缓存时）
    return {
      user: null,
      isLoading: true,
      isAuthenticated: false,
    };
  });

  // 添加一个标记来跟踪是否已经挂载
  const [isMounted, setIsMounted] = useState(false);

  // 检查认证状态
  const checkAuth = useCallback(async () => {
    console.log("🔐 checkAuth开始执行");
    try {
      // 先检查客户端环境
      if (typeof window === "undefined") {
        console.log("🔐 checkAuth: 服务端环境，跳过检查");
        setAuthState((prev) => ({ ...prev, isLoading: false }));
        return;
      }

      const isAuth = isAuthenticated();
      console.log("🔐 checkAuth: isAuthenticated()结果:", isAuth);

      if (!isAuth) {
        console.log("🔐 checkAuth: 未认证，清除状态");
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        });
        return;
      }

      // 获取用户信息
      console.log("🔐 checkAuth: 开始获取用户信息");
      const user = await getCurrentUser();
      console.log("🔐 checkAuth: getCurrentUser()结果:", user?.username);

      if (user) {
        console.log("🔐 checkAuth: 设置认证状态为已登录");
        setAuthState({
          user,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        // 如果获取用户信息失败，检查是否仍有有效token
        const token = localStorage.getItem("auth_token");
        console.log("🔐 checkAuth: 获取用户信息失败，检查token:", !!token);

        if (token) {
          console.warn(
            "🔐 checkAuth: 有token但获取用户信息失败，可能是后端兼容性问题"
          );
          // 保持认证状态，但用户信息为空
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: true, // 保持认证状态
          });
        } else {
          // 没有token，清除认证状态
          console.log("🔐 checkAuth: 无token，清除认证状态");
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          });
        }
      }
    } catch (error) {
      console.error("🔐 checkAuth: 认证检查失败:", error);
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
    console.log("🔐 checkAuth执行完成");
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      const result = await authLogout();
      if (result.success) {
        showToast(result.message, "success");
      }
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
      router.push("/login");
    }
  }, [router, showToast]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    if (!isAuthenticated()) return;

    try {
      const user = await getCurrentUser();
      if (user) {
        setAuthState((prev) => ({
          ...prev,
          user,
        }));
      }
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  }, []);

  // 设置用户信息
  const setUser = useCallback(
    (user: User | null) => {
      console.log("🔐 setUser被调用:", {
        user: user?.username,
        hasUser: !!user,
      });

      // 使用 React 的批量更新确保状态同步更新
      setAuthState((prev) => {
        const newState = {
          ...prev,
          user,
          isAuthenticated: !!user, // 同时更新认证状态
          isLoading: false, // 确保加载状态被清除
        };

        console.log("🔐 setUser状态更新:", {
          prevAuth: prev.isAuthenticated,
          newAuth: newState.isAuthenticated,
          prevUser: prev.user?.username,
          newUser: newState.user?.username,
        });

        return newState;
      });

      // 强制更新挂载状态，确保认证状态立即生效
      if (user && !isMounted) {
        setIsMounted(true);
      }
    },
    [isMounted]
  );

  // 检查是否有特定权限
  const hasPermission = useCallback(
    (requiredRole: string) => {
      if (!authState.user || !authState.user.role) {
        return false;
      }

      const roleHierarchy: { [key: string]: number } = {
        user: 1,
        moderator: 2,
        admin: 3,
      };

      const userLevel = roleHierarchy[authState.user.role.name] || 0;
      const requiredLevel = roleHierarchy[requiredRole] || 0;
      const hasAccess = userLevel >= requiredLevel;

      return hasAccess;
    },
    [authState.user]
  );

  // 检查是否为管理员
  const isAdmin = useCallback(() => {
    const result = authState.user?.role?.name === "admin";
    console.log("🔐 useAuth.isAdmin:", {
      userRole: authState.user?.role,
      isAdmin: result,
    });
    return result;
  }, [authState.user]);

  // 检查是否为版主或管理员
  const isModerator = useCallback(() => {
    const result =
      authState.user?.role?.name === "moderator" ||
      authState.user?.role?.name === "admin";
    console.log("🔐 useAuth.isModerator:", {
      userRole: authState.user?.role,
      isModerator: result,
    });
    return result;
  }, [authState.user]);

  // 要求登录
  const requireAuth = useCallback(
    (redirectTo?: string) => {
      if (!authState.isAuthenticated) {
        const currentPath = window.location.pathname + window.location.search;
        const redirect = redirectTo || currentPath;
        router.push(`/login?redirect=${encodeURIComponent(redirect)}`);
        return false;
      }
      return true;
    },
    [authState.isAuthenticated, router]
  );

  // 要求特定权限
  const requirePermission = useCallback(
    (requiredRole: string, redirectTo?: string) => {
      if (!requireAuth(redirectTo)) return false;

      if (!hasPermission(requiredRole)) {
        showToast("您没有权限访问此页面", "error");
        router.push("/");
        return false;
      }

      return true;
    },
    [requireAuth, hasPermission, showToast, router]
  );

  // 要求管理员权限
  const requireAdmin = useCallback(
    (redirectTo?: string) => {
      return requirePermission("admin", redirectTo);
    },
    [requirePermission]
  );

  // 组件挂载后设置挂载状态并检查认证
  useEffect(() => {
    console.log("🔐 useAuth组件挂载，当前状态:", {
      isAuthenticated: authState.isAuthenticated,
      user: authState.user?.username,
      isLoading: authState.isLoading,
    });

    setIsMounted(true);

    // 如果初始化时已经有用户信息，仍然需要验证token有效性
    if (authState.user && authState.isAuthenticated) {
      console.log("🔐 useAuth: 有缓存状态，验证token有效性");
      // 异步验证，但不阻塞初始渲染
      setTimeout(() => {
        checkAuth();
      }, 100);
    } else {
      console.log("🔐 useAuth: 无缓存状态，立即检查认证");
      checkAuth();
    }
  }, [checkAuth, authState.user, authState.isAuthenticated]); // 添加所有依赖

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "auth_token") {
        if (!e.newValue) {
          // Token被删除，用户在其他标签页登出
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          });
        } else {
          // Token被更新，重新检查认证状态
          // 直接调用checkAuth，不依赖useCallback
          (async () => {
            try {
              const isAuth = isAuthenticated();
              if (!isAuth) {
                setAuthState({
                  user: null,
                  isLoading: false,
                  isAuthenticated: false,
                });
                return;
              }

              const user = await getCurrentUser();
              if (user) {
                setAuthState({
                  user,
                  isLoading: false,
                  isAuthenticated: true,
                });
              } else {
                setAuthState({
                  user: null,
                  isLoading: false,
                  isAuthenticated: false,
                });
              }
            } catch {
              setAuthState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
              });
            }
          })();
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []); // 空依赖数组

  // 记录状态变化用于调试
  useEffect(() => {
    authStateTracker.recordSnapshot("useAuth-return", {
      isAuthenticated: isMounted ? authState.isAuthenticated : false,
      user: authState.user,
      isLoading: authState.isLoading || !isMounted,
    });
  }, [authState, isMounted]);

  return {
    // 状态 - 在未挂载时确保返回安全的默认值
    user: authState.user,
    isLoading: authState.isLoading || !isMounted, // 未挂载时也显示为加载中
    isAuthenticated: isMounted ? authState.isAuthenticated : false, // 未挂载时始终为false

    // 方法
    logout,
    refreshUser,
    checkAuth,
    setUser,

    // 权限检查
    hasPermission,
    isAdmin,
    isModerator,

    // 权限要求
    requireAuth,
    requirePermission,
    requireAdmin,
  };
}
