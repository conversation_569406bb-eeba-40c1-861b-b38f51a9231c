import { NextRequest, NextResponse } from "next/server";

const BACKEND_API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authHeader = request.headers.get("authorization");

    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          message: "未提供认证信息",
          error: "Missing authorization header",
        },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();

    // 转发请求到后端服务
    const response = await fetch(
      `${BACKEND_API_URL}/api/help/requests/${id}/adopt`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: authHeader,
        },
        body: JSON.stringify(body),
      }
    );

    // 获取后端响应数据
    const data = await response.json();

    // 如果后端返回错误状态码，保持相同的状态码
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // 返回成功响应
    return NextResponse.json(data);
  } catch (error) {
    console.error("采纳答案失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器处理请求时出错",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}