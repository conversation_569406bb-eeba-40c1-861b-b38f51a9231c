/**
 * 求助功能SEO配置
 * 生成求助相关页面的SEO元数据
 */

import { Metadata } from "next";
import { HelpRequest } from "@/types/help-request";


// 生成求助列表页面SEO
export function generateHelpRequestListSEO(): Metadata {
  return {
    title: "资源求助 - 97盘搜",
    description: "在97盘搜发布资源求助，获得社区帮助找到您需要的网盘资源。支持百度网盘、夸克网盘、阿里云盘、迅雷网盘等多种网盘类型。",
    keywords: "资源求助,网盘资源,百度网盘,夸克网盘,阿里云盘,迅雷网盘,社区互助,资源分享",
    openGraph: {
      title: "资源求助 - 97盘搜",
      description: "在97盘搜发布资源求助，获得社区帮助找到您需要的网盘资源",
      type: "website",
      siteName: "97盘搜",
    },
    twitter: {
      card: "summary",
      title: "资源求助 - 97盘搜",
      description: "在97盘搜发布资源求助，获得社区帮助找到您需要的网盘资源",
    },
    alternates: {
      canonical: "/help-requests",
    },
  };
}

// 生成求助详情页面SEO
export function generateHelpRequestDetailSEO(helpRequest: HelpRequest): Metadata {
  const panTypes = helpRequest.cloud_disk_types.join('、');
  const description = helpRequest.description 
    ? `${helpRequest.description.substring(0, 150)}...`
    : `求助：${helpRequest.title}。支持${panTypes}等网盘类型。`;

  return {
    title: `${helpRequest.title} - 资源求助 - 97盘搜`,
    description,
    keywords: `资源求助,${helpRequest.title},网盘资源,${panTypes},${helpRequest.resource_type}`,
    openGraph: {
      title: helpRequest.title,
      description,
      type: "article",
      siteName: "97盘搜",
      publishedTime: helpRequest.created_at,
      modifiedTime: helpRequest.updated_at,
      authors: [helpRequest.requester.username],
      tags: [
        '资源求助',
        ...helpRequest.cloud_disk_types,
        helpRequest.resource_type,
        ...(helpRequest.tags || []),
      ],
    },
    twitter: {
      card: "summary",
      title: helpRequest.title,
      description,
    },
    alternates: {
      canonical: `/help-requests/${helpRequest.id}`,
    },
  };
}

// 生成发布求助页面SEO
export function generateCreateHelpRequestSEO(): Metadata {
  return {
    title: "发布求助 - 资源求助 - 97盘搜",
    description: "在97盘搜发布资源求助，向社区寻求帮助找到您需要的网盘资源。支持多种网盘类型和资源类型。",
    keywords: "发布求助,资源求助,网盘资源,百度网盘,夸克网盘,阿里云盘,迅雷网盘",
    openGraph: {
      title: "发布求助 - 97盘搜",
      description: "在97盘搜发布资源求助，向社区寻求帮助找到您需要的网盘资源",
      type: "website",
      siteName: "97盘搜",
    },
    robots: {
      index: false, // 发布页面不需要被索引
      follow: true,
    },
    alternates: {
      canonical: "/help-requests/create",
    },
  };
}

// 生成我的求助页面SEO
export function generateMyHelpRequestsSEO(): Metadata {
  return {
    title: "我的求助 - 资源求助 - 97盘搜",
    description: "管理您在97盘搜发布的资源求助和回答，查看求助状态和统计信息。",
    keywords: "我的求助,资源求助管理,求助统计",
    robots: {
      index: false, // 个人页面不需要被索引
      follow: false,
    },
    alternates: {
      canonical: "/help-requests/my",
    },
  };
}

// 生成结构化数据
export function generateHelpRequestStructuredData(helpRequest: HelpRequest) {
  return {
    "@context": "https://schema.org",
    "@type": "Question",
    "name": helpRequest.title,
    "text": helpRequest.description || helpRequest.title,
    "dateCreated": helpRequest.created_at,
    "dateModified": helpRequest.updated_at,
    "author": {
      "@type": "Person",
      "name": helpRequest.requester.username,
    },
    "answerCount": helpRequest.answer_count,
    "upvoteCount": 0, // 可以后续添加点赞功能
    "acceptedAnswer": undefined, // HelpRequest doesn't have best_answer_id
    "about": {
      "@type": "Thing",
      "name": "网盘资源",
      "description": `关于${helpRequest.cloud_disk_types.join('、')}的资源求助`,
    },
    "keywords": [
      "资源求助",
      helpRequest.title,
      ...helpRequest.cloud_disk_types,
      helpRequest.resource_type,
      ...(helpRequest.tags || []),
    ].join(","),
  };
}

// 生成求助列表结构化数据
export function generateHelpRequestListStructuredData(helpRequests: HelpRequest[]) {
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "资源求助列表",
    "description": "97盘搜用户发布的资源求助列表",
    "numberOfItems": helpRequests.length,
    "itemListElement": helpRequests.map((helpRequest, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Question",
        "@id": `/help-requests/${helpRequest.id}`,
        "name": helpRequest.title,
        "text": helpRequest.description || helpRequest.title,
        "dateCreated": helpRequest.created_at,
        "author": {
          "@type": "Person",
          "name": helpRequest.requester.username,
        },
        "answerCount": helpRequest.answer_count,
      },
    })),
  };
}

// 面包屑导航结构化数据
export function generateBreadcrumbStructuredData(items: Array<{ name: string; url?: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url ? {
        "@type": "WebPage",
        "@id": item.url,
      } : undefined,
    })),
  };
}

// 网站搜索框结构化数据
export function generateSearchBoxStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://97pansou.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://97pansou.com/search?q={search_term_string}",
      },
      "query-input": "required name=search_term_string",
    },
  };
}

// 组织信息结构化数据
export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "97盘搜",
    "url": "https://97pansou.com",
    "logo": "https://97pansou.com/logo.png",
    "description": "专业的网盘资源搜索引擎，提供百度网盘、夸克网盘、阿里云盘等多种网盘资源搜索服务",
    "sameAs": [
      // 可以添加社交媒体链接
    ],
  };
}
