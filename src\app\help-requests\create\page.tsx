"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

import { useToast } from "@/components/ToastProvider";
import AuthGuard from "@/components/AuthGuard";
import { createHelpRequest } from "@/services/helpRequestService";
import {
  CreateHelpRequestData,
  CLOUD_DISK_TYPES,
  RESOURCE_TYPES,
} from "@/types/help-request";

function CreateHelpRequestForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showToast } = useToast();
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<CreateHelpRequestData>({
    title: "",
    description: null,
    cloud_disk_types: ["baidu", "quark", "aliyun", "xunlei"], // 默认选择主流网盘
    resource_type: "other", // 默认为其他类型
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 从URL参数中获取搜索关键词作为默认标题
  useEffect(() => {
    const query = searchParams?.get("q");
    if (query) {
      setFormData((prev) => ({ ...prev, title: query }));
    }
  }, [searchParams]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "资源名称不能为空";
    } else if (formData.title.trim().length < 1) {
      newErrors.title = "资源名称至少需要1个字符";
    } else if (formData.title.trim().length > 200) {
      newErrors.title = "资源名称不能超过200个字符";
    }

    if (formData.description && formData.description.length > 1000) {
      newErrors.description = "资源描述不能超过1000个字符";
    }

    if (formData.cloud_disk_types.length === 0) {
      newErrors.cloud_disk_types = "请至少选择一种网盘类型";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    try {
      const response = await createHelpRequest(formData);
      if (response.success) {
        showToast("求助发布成功", "success");
        router.push("/help-requests");
      } else {
        showToast(response.message || "发布求助失败", "error");
      }
    } catch {
      showToast("发布求助失败", "error");
    } finally {
      setSubmitting(false);
    }
  };

  const handleCloudDiskTypeChange = (diskType: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        cloud_disk_types: [...prev.cloud_disk_types, diskType],
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        cloud_disk_types: prev.cloud_disk_types.filter((type) => type !== diskType),
      }));
    }
  };

  const handleResourceTypeChange = (resourceType: string) => {
    setFormData((prev) => ({
      ...prev,
      resource_type: resourceType as CreateHelpRequestData['resource_type'],
    }));
  };

  const handleSelectAllCloudDiskTypes = () => {
    const allDiskTypes = CLOUD_DISK_TYPES.map(type => type.value);
    setFormData((prev) => ({ ...prev, cloud_disk_types: allDiskTypes }));
  };

  const handleClearAllCloudDiskTypes = () => {
    setFormData((prev) => ({ ...prev, cloud_disk_types: [] }));
  };

  return (
    <div>
      {/* 返回按钮 */}
      <div className="mb-6">
        <Link
          href="/help-requests"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          返回求助列表
        </Link>
      </div>

      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          发布求助
        </h1>
        <p className="text-secondary-text mt-1">
          向社区求助，获得其他用户的帮助找到您需要的资源
        </p>
      </div>

      {/* 发布表单 */}
      <div className="bg-card-background border border-border-color rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 资源名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              资源名称 *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              placeholder="请输入您要寻找的资源名称"
              className={`w-full px-3 py-2 border rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.title ? "border-red-500" : "border-border-color"
              }`}
              maxLength={200}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title}</p>
            )}
            <p className="mt-1 text-xs text-secondary-text">
              {formData.title.length}/200 字符
            </p>
          </div>

          {/* 网盘类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              网盘类型 * <span className="text-secondary-text">(可多选)</span>
            </label>
            <div className="space-y-2">
              <div className="flex gap-2 mb-3">
                <button
                  type="button"
                  onClick={handleSelectAllCloudDiskTypes}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors"
                >
                  全选
                </button>
                <button
                  type="button"
                  onClick={handleClearAllCloudDiskTypes}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded hover:bg-gray-200 dark:hover:bg-gray-900/30 transition-colors"
                >
                  清空
                </button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {CLOUD_DISK_TYPES.map((diskType) => (
                  <label key={diskType.value} className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.cloud_disk_types.includes(diskType.value)}
                      onChange={(e) =>
                        handleCloudDiskTypeChange(diskType.value, e.target.checked)
                      }
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border-color rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900 dark:text-gray-100">
                      {diskType.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>
            {errors.cloud_disk_types && (
              <p className="mt-1 text-sm text-red-600">{errors.cloud_disk_types}</p>
            )}
          </div>

          {/* 资源类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              资源类型
            </label>
            <select
              value={formData.resource_type}
              onChange={(e) => handleResourceTypeChange(e.target.value)}
              className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {RESOURCE_TYPES.map((resourceType) => (
                <option key={resourceType.value} value={resourceType.value}>
                  {resourceType.label}
                </option>
              ))}
            </select>
          </div>

          {/* 资源描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              资源描述 <span className="text-secondary-text">(可选)</span>
            </label>
            <textarea
              value={formData.description || ""}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value || null })
              }
              placeholder="请详细描述您要寻找的资源，包括版本、格式、语言等信息，这有助于其他用户更好地帮助您"
              rows={4}
              className={`w-full px-3 py-2 border rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.description ? "border-red-500" : "border-border-color"
              }`}
              maxLength={1000}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
            <p className="mt-1 text-xs text-secondary-text">
              {(formData.description || "").length}/1000 字符
            </p>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3">
            <Link
              href="/help-requests"
              className="px-4 py-2 border border-border-color text-gray-900 dark:text-gray-100 rounded-md hover:bg-hover-background transition-colors"
            >
              取消
            </Link>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {submitting ? "发布中..." : "发布求助"}
            </button>
          </div>
        </form>
      </div>

      {/* 发布须知 */}
      <div className="mt-6 bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          发布须知
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• 请确保求助内容真实有效，不要发布虚假信息</li>
          <li>• 详细描述您需要的资源有助于获得更好的帮助</li>
          <li>• 当有用户提供帮助时，请及时采纳最佳答案</li>
          <li>• 请遵守社区规则，不要发布违法违规内容</li>
          <li>• 感谢每一位提供帮助的用户，共建和谐社区</li>
        </ul>
      </div>
    </div>
  );
}

export default function CreateHelpRequestPage() {
  return (
    <AuthGuard requireAuth={true}>
      <CreateHelpRequestForm />
    </AuthGuard>
  );
}
