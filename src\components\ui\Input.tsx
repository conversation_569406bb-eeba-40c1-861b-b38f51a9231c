"use client";

import { InputHTMLAttributes, forwardRef } from "react";
import { cn } from "@/lib/utils";

export type InputProps = InputHTMLAttributes<HTMLInputElement>;

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type || "text"}
        className={cn(
          "w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]",
          // 彻底移除浏览器自带的密码显示按钮
          type === "password" && "password-input-custom",
          className
        )}
        ref={ref}
        {...props}
        {...(type === "password" && {
          autoComplete: props.autoComplete || "new-password",
          spellCheck: false,
        })}
      />
    );
  }
);

Input.displayName = "Input";

export { Input };
