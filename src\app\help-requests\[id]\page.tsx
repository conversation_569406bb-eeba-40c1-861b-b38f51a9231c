import { getHelpRequestDetailSSR } from "@/services/helpRequestService";
import { type HelpRequestDetail } from "@/types/help-request";
import HelpRequestClient from "./HelpRequestClient";
import ErrorPageClient from "./ErrorPageClient";
import { Metadata } from "next";

interface Props {
  params: Promise<{ id: string }>;
}

// 生成页面元数据
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params;
  
  try {
    const response = await getHelpRequestDetailSSR(id);
    
    if (response.status === "success" && response.data) {
      const helpRequest: HelpRequestDetail = response.data;
      
      // 生成网盘类型文本
      const getDiskTypeText = (diskTypes: string[]) => {
        if (!diskTypes || diskTypes.length === 0) return "";
        
        const typeMap: { [key: string]: string } = {
          baidu: "百度网盘",
          aliyun: "阿里云盘", 
          quark: "夸克网盘",
          xunlei: "迅雷网盘",
        };
        
        return diskTypes.map((type) => typeMap[type] || type).join("、");
      };
      
      const diskTypeText = getDiskTypeText(helpRequest.cloud_disk_types || []);
      const title = diskTypeText 
        ? `${helpRequest.title} - ${diskTypeText} - 盘搜资源求助`
        : `${helpRequest.title} - 盘搜资源求助`;
      
      const description = helpRequest.description 
        ? `${helpRequest.description.substring(0, 160)}...`
        : `求助资源：${helpRequest.title}，支持${diskTypeText}等网盘类型，已有${helpRequest.answer_count}个回复。`;
        
      return {
        title,
        description,
        keywords: [
          helpRequest.title,
          diskTypeText,
          "资源求助",
          "网盘资源",
          "资源分享"
        ].filter(Boolean).join(", "),
        openGraph: {
          title,
          description,
          type: "article",
          publishedTime: helpRequest.created_at,
          modifiedTime: helpRequest.updated_at,
        },
        twitter: {
          card: "summary",
          title,
          description,
        },
      };
    }
  } catch (error) {
    console.error("生成元数据失败:", error);
  }
  
  return {
    title: "资源求助详情 - 盘搜",
    description: "盘搜资源求助平台，帮助用户快速找到所需资源",
  };
}

export default async function HelpRequestDetailPage({ params }: Props) {
  const { id } = await params;
  
  // 服务器端获取数据
  const response = await getHelpRequestDetailSSR(id);
  
  // 错误处理 - 改进用户体验
  if (response.status === "error" || !response.data) {
    const isNotFound = response.error?.includes("404") || response.message?.includes("不存在");
    
    return (
      <ErrorPageClient 
        isNotFound={isNotFound} 
        message={response.message || ""} 
      />
    );
  }

  const helpRequest: HelpRequestDetail = response.data;

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "QAPage",
            "mainEntity": {
              "@type": "Question",
              "name": helpRequest.title,
              "text": helpRequest.description || helpRequest.title,
              "answerCount": helpRequest.answer_count,
              "dateCreated": helpRequest.created_at,
              "author": {
                "@type": "Person",
                "name": helpRequest.requester.nickname || helpRequest.requester.username,
              },
              "acceptedAnswer": helpRequest.answers.find(a => a.is_accepted) ? {
                "@type": "Answer",
                "text": helpRequest.answers.find(a => a.is_accepted)?.resource_title,
                "dateCreated": helpRequest.answers.find(a => a.is_accepted)?.created_at,
                "author": {
                  "@type": "Person", 
                  "name": helpRequest.answers.find(a => a.is_accepted)?.answerer.nickname || helpRequest.answers.find(a => a.is_accepted)?.answerer.username,
                }
              } : undefined,
              "suggestedAnswer": helpRequest.answers.map(answer => ({
                "@type": "Answer",
                "text": answer.resource_title,
                "dateCreated": answer.created_at,
                "author": {
                  "@type": "Person",
                  "name": answer.answerer.nickname || answer.answerer.username,
                }
              }))
            }
          })
        }}
      />
      
      {/* 客户端组件 */}
      <HelpRequestClient initialData={helpRequest} requestId={id} />
    </>
  );
}