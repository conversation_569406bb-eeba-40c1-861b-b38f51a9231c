/**
 * 认证状态持久化测试
 * 验证登录后状态在页面跳转时的持久化效果
 */

import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock authStateTracker
vi.mock("@/utils/authStateTracker", () => ({
  authStateTracker: {
    recordSnapshot: vi.fn(),
  },
}));

describe("认证状态持久化", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe("localStorage 缓存恢复", () => {
    it("应该从 localStorage 恢复用户状态", () => {
      const mockUser = {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      };

      localStorageMock.getItem.mockImplementation((key: string) => {
        switch (key) {
          case "auth_token":
            return "mock-token";
          case "auth_user":
            return JSON.stringify(mockUser);
          case "auth_expires":
            return new Date(Date.now() + 3600000).toISOString(); // 1小时后过期
          default:
            return null;
        }
      });

      // 模拟 useAuth 初始化逻辑
      const hasToken = !!localStorage.getItem("auth_token");
      const cachedUser = localStorage.getItem("auth_user");
      
      expect(hasToken).toBe(true);
      expect(cachedUser).toBeTruthy();
      
      if (hasToken && cachedUser) {
        const user = JSON.parse(cachedUser);
        expect(user.username).toBe("testuser");
        expect(user.email).toBe("<EMAIL>");
      }
    });

    it("应该处理损坏的缓存数据", () => {
      localStorageMock.getItem.mockImplementation((key: string) => {
        switch (key) {
          case "auth_token":
            return "mock-token";
          case "auth_user":
            return "invalid-json"; // 损坏的JSON
          default:
            return null;
        }
      });

      const hasToken = !!localStorage.getItem("auth_token");
      const cachedUser = localStorage.getItem("auth_user");
      
      expect(hasToken).toBe(true);
      
      let parsedUser = null;
      if (hasToken && cachedUser) {
        try {
          parsedUser = JSON.parse(cachedUser);
        } catch (error) {
          // 应该捕获解析错误
          expect(error).toBeInstanceOf(SyntaxError);
        }
      }
      
      expect(parsedUser).toBeNull();
    });

    it("应该检查 token 过期时间", () => {
      const expiredTime = new Date(Date.now() - 3600000).toISOString(); // 1小时前过期
      
      localStorageMock.getItem.mockImplementation((key: string) => {
        switch (key) {
          case "auth_token":
            return "mock-token";
          case "auth_expires":
            return expiredTime;
          default:
            return null;
        }
      });

      // 模拟 isAuthenticated 检查逻辑
      const token = localStorage.getItem("auth_token");
      const expires = localStorage.getItem("auth_expires");
      
      let isValid = false;
      if (token) {
        if (expires) {
          const expiresDate = new Date(expires);
          const now = new Date();
          isValid = expiresDate > now;
        } else {
          isValid = true; // 没有过期时间则认为有效
        }
      }
      
      expect(isValid).toBe(false); // token已过期
    });
  });

  describe("状态同步", () => {
    it("应该在有缓存时立即返回认证状态", () => {
      const mockUser = {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      };

      localStorageMock.getItem.mockImplementation((key: string) => {
        switch (key) {
          case "auth_token":
            return "mock-token";
          case "auth_user":
            return JSON.stringify(mockUser);
          default:
            return null;
        }
      });

      // 模拟 useAuth 初始化状态计算
      let initialState;
      if (typeof window !== "undefined") {
        const hasToken = !!localStorage.getItem("auth_token");
        const cachedUser = localStorage.getItem("auth_user");
        
        if (hasToken && cachedUser) {
          try {
            const user = JSON.parse(cachedUser);
            initialState = {
              user,
              isLoading: false,
              isAuthenticated: true,
            };
          } catch (error) {
            initialState = {
              user: null,
              isLoading: true,
              isAuthenticated: false,
            };
          }
        } else {
          initialState = {
            user: null,
            isLoading: true,
            isAuthenticated: false,
          };
        }
      }

      expect(initialState?.isAuthenticated).toBe(true);
      expect(initialState?.isLoading).toBe(false);
      expect(initialState?.user?.username).toBe("testuser");
    });

    it("应该在无缓存时返回默认状态", () => {
      localStorageMock.getItem.mockReturnValue(null);

      // 模拟 useAuth 初始化状态计算
      let initialState;
      if (typeof window !== "undefined") {
        const hasToken = !!localStorage.getItem("auth_token");
        const cachedUser = localStorage.getItem("auth_user");
        
        if (hasToken && cachedUser) {
          try {
            const user = JSON.parse(cachedUser);
            initialState = {
              user,
              isLoading: false,
              isAuthenticated: true,
            };
          } catch (error) {
            initialState = {
              user: null,
              isLoading: true,
              isAuthenticated: false,
            };
          }
        } else {
          initialState = {
            user: null,
            isLoading: true,
            isAuthenticated: false,
          };
        }
      }

      expect(initialState?.isAuthenticated).toBe(false);
      expect(initialState?.isLoading).toBe(true);
      expect(initialState?.user).toBeNull();
    });
  });
});
