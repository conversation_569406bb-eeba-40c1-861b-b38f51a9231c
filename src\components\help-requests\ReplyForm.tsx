"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ToastProvider";
import { useAuth } from "@/hooks/useAuth";
import { createHelpAnswer } from "@/services/helpRequestService";

interface ReplyFormProps {
  requestId: string | number;
  onReplySuccess?: () => void;
}

const CLOUD_DISK_OPTIONS = [
  { value: "baidu", label: "百度网盘" },
  { value: "aliyun", label: "阿里云盘" },
  { value: "quark", label: "夸克网盘" },
  { value: "xunlei", label: "迅雷网盘" },
];

export default function ReplyForm({ requestId, onReplySuccess }: ReplyFormProps) {
  const { showToast } = useToast();
  const { isAuthenticated } = useAuth();
  
  const [formData, setFormData] = useState({
    resource_title: "",
    resource_link: "",
    cloud_disk_type: "",
    additional_info: "",
    should_archive: false,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      showToast("请先登录后再回复", "error");
      return;
    }

    if (!formData.resource_title.trim()) {
      showToast("请输入资源标题", "error");
      return;
    }

    if (!formData.resource_link.trim()) {
      showToast("请输入资源链接", "error");
      return;
    }

    if (!formData.cloud_disk_type) {
      showToast("请选择网盘类型", "error");
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await createHelpAnswer(requestId, {
        resource_title: formData.resource_title.trim(),
        resource_link: formData.resource_link.trim(),
        cloud_disk_type: formData.cloud_disk_type,
        additional_info: formData.additional_info.trim() || undefined,
        should_archive: formData.should_archive,
      });

      if (response.success) {
        showToast("回复成功！", "success");
        setFormData({
          resource_title: "",
          resource_link: "",
          cloud_disk_type: "",
          additional_info: "",
          should_archive: false,
        });
        onReplySuccess?.();
      } else {
        showToast(response.message || "回复失败", "error");
      }
    } catch (error) {
      console.error("提交回复失败:", error);
      showToast("提交失败，请稍后重试", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
        <p className="text-gray-500 dark:text-gray-400 mb-4">请登录后回复求助</p>
        <Button
          variant="outline"
          onClick={() => window.location.href = "/login"}
        >
          立即登录
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="p-4">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          回复求助
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 资源标题 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1.5">
              资源标题 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.resource_title}
              onChange={(e) => setFormData(prev => ({ ...prev, resource_title: e.target.value }))}
              placeholder="请输入资源标题"
              className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isSubmitting}
            />
          </div>

          {/* 资源链接 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1.5">
              资源链接 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.resource_link}
              onChange={(e) => setFormData(prev => ({ ...prev, resource_link: e.target.value }))}
              placeholder="请输入网盘分享链接"
              className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isSubmitting}
            />
          </div>

          {/* 网盘类型 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1.5">
              网盘类型 <span className="text-red-500">*</span>
            </label>
            <div className="flex flex-wrap gap-2">
              {CLOUD_DISK_OPTIONS.map((option) => (
                <label
                  key={option.value}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="cloud_disk_type"
                    value={option.value}
                    checked={formData.cloud_disk_type === option.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, cloud_disk_type: e.target.value }))}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                    disabled={isSubmitting}
                  />
                  <span className="text-xs text-gray-700 dark:text-gray-300">
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* 附加信息 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1.5">
              附加信息
            </label>
            <Textarea
              value={formData.additional_info}
              onChange={(e) => setFormData(prev => ({ ...prev, additional_info: e.target.value }))}
              placeholder="可选：提供额外的说明信息..."
              rows={3}
              className="resize-none text-sm"
              disabled={isSubmitting}
            />
          </div>

          {/* 是否应该归档 */}
          <div>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.should_archive}
                onChange={(e) => setFormData(prev => ({ ...prev, should_archive: e.target.checked }))}
                className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                disabled={isSubmitting}
              />
              <span className="text-xs text-gray-700 dark:text-gray-300">
                建议归档此资源
              </span>
            </label>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end pt-2">
            <Button
              type="submit"
              disabled={isSubmitting}
              size="sm"
            >
              {isSubmitting ? "提交中..." : "提交回复"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}