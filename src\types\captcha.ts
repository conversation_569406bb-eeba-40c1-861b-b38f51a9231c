/**
 * ALTCHA验证码相关类型定义
 */

// ALTCHA验证负载接口
export interface AltchaPayload {
  algorithm: string;
  challenge: string;
  number: number;
  salt: string;
  signature: string;
}

// 挑战响应接口
export interface ChallengeResponse {
  algorithm: string;
  challenge: string;
  salt: string;
  signature: string;
  maxnumber?: number;
}

// 验证请求接口
export interface VerifyRequest {
  payload: string;
}

// 验证响应接口
export interface VerifyResponse {
  verified: boolean;
  message?: string;
}

// ALTCHA组件事件接口
export interface AltchaVerifiedEvent {
  detail: {
    payload: string;
    verified: boolean;
  };
}

export interface AltchaErrorEvent {
  detail: {
    error: string;
    message?: string;
  };
}

export interface AltchaStateChangeEvent {
  detail: {
    state: 'unverified' | 'verifying' | 'verified' | 'error';
  };
}

// 注册请求扩展接口（包含验证码）
export interface RegisterRequestWithCaptcha {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  agree_terms: boolean;
  captcha: string; // ALTCHA验证负载
}

// ALTCHA配置接口
export interface AltchaConfig {
  challengeUrl: string;
  maxNumber?: number;
  strings?: {
    label?: string;
    verifying?: string;
    verified?: string;
    error?: string;
  };
  debug?: boolean;
}

// Web Component类型声明（用于TypeScript支持）
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'altcha-widget': {
        ref?: React.Ref<any>;
        challengeurl?: string;
        strings?: string;
        maxnumber?: number;
        debug?: boolean;
        style?: React.CSSProperties;
        [key: string]: any;
      };
    }
  }
}