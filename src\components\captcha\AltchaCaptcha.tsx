'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Shield, CheckCircle2 } from 'lucide-react';

interface AltchaCaptchaProps {
  onVerified: (payload: string) => void;
  onError?: (error: string) => void;
  challengeUrl?: string;
  className?: string;
  disabled?: boolean;
}

export function AltchaCaptcha({ 
  onVerified, 
  onError, 
  challengeUrl = "/api/captcha/challenge",
  className = "",
  disabled = false
}: AltchaCaptchaProps) {
  const altchaRef = useRef<any>(null);
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 使用useRef来保存最新的回调函数，避免useCallback依赖问题
  const onVerifiedRef = useRef(onVerified);
  const onErrorRef = useRef(onError);

  // 确保ref中总是最新的回调函数
  useEffect(() => {
    onVerifiedRef.current = onVerified;
    onErrorRef.current = onError;
    console.log('更新回调函数引用:', { 
      onVerified: typeof onVerified, 
      onError: typeof onError 
    });
  }, [onVerified, onError]); // 添加依赖以确保及时更新

  useEffect(() => {
    let mounted = true;

    const initializeAltcha = async () => {
      // 避免重复初始化
      if (isInitialized) {
        console.log('已初始化，跳过重复初始化');
        return;
      }
      
      try {
        setIsLoading(true);
        setError(null);

        // 动态导入ALTCHA
        await import('altcha');
        console.log('ALTCHA 库导入成功');
        
        if (!mounted) {
          console.log('组件已卸载，停止初始化');
          return;
        }

        // 等待组件挂载
        await new Promise(resolve => setTimeout(resolve, 100));

        if (altchaRef.current && mounted) {
          console.log('ALTCHA 组件引用获取成功，开始添加事件监听器');
          // 监听验证成功事件
          altchaRef.current.addEventListener('verified', (event: any) => {
            console.log('verified事件触发，mounted状态:', mounted);
            if (mounted) {
              console.log('ALTCHA verified event:', event.detail); // 调试日志
              setIsVerified(true);
              setError(null);
              onVerifiedRef.current(event.detail.payload);
            }
          });

          // 监听验证失败事件
          altchaRef.current.addEventListener('error', (event: any) => {
            console.log('error事件触发，mounted状态:', mounted);
            if (mounted) {
              console.log('ALTCHA error event:', event.detail); // 调试日志
              const errorMessage = event.detail?.message || '验证失败，请重试';
              setError(errorMessage);
              setIsVerified(false);
              onErrorRef.current?.(errorMessage);
            }
          });

          // 监听加载完成事件
          altchaRef.current.addEventListener('statechange', (event: any) => {
            console.log('ALTCHA state change:', event.detail, '- mounted状态:', mounted);
            
            // 移除mounted检查，直接处理verified状态
            if (event.detail?.state === 'verified') {
              console.log('状态为verified，开始处理');
              setIsLoading(false);
              
              // 当状态变为verified时，也需要更新验证状态
              if (event.detail?.payload) {
                console.log('从statechange事件触发验证成功，payload存在');
                console.log('当前onVerifiedRef:', onVerifiedRef.current);
                console.log('准备调用onVerifiedRef.current，payload:', event.detail.payload);
                
                setIsVerified(true);
                setError(null);
                
                // 确保回调函数存在再调用
                if (typeof onVerifiedRef.current === 'function') {
                  onVerifiedRef.current(event.detail.payload);
                  console.log('已调用onVerifiedRef.current');
                } else {
                  console.error('onVerifiedRef.current 不是函数:', typeof onVerifiedRef.current);
                }
              } else {
                console.log('payload不存在，跳过验证处理');
              }
            } else if (event.detail?.state === 'verifying') {
              console.log('状态为verifying');
            } else {
              console.log('状态不是verified，当前状态:', event.detail?.state);
            }
          });

          setIsLoading(false);
          setIsInitialized(true);
          console.log('ALTCHA初始化完成');
        }
      } catch (error) {
        if (mounted) {
          console.error('ALTCHA initialization error:', error);
          const errorMessage = '验证组件加载失败';
          setError(errorMessage);
          setIsLoading(false);
          onErrorRef.current?.(errorMessage);
        }
      }
    };

    initializeAltcha();

    return () => {
      console.log('ALTCHA组件cleanup，设置mounted=false');
      mounted = false;
    };
  }, [challengeUrl, isInitialized]); // 移除isInitialized依赖，避免重复初始化

  // 重置验证状态
  const resetCaptcha = useCallback(() => {
    setIsVerified(false);
    setError(null);
    if (altchaRef.current) {
      altchaRef.current.reset();
    }
  }, []);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 验证状态提示 */}
      <div className="flex items-center space-x-2 text-sm">
        <Shield className="h-4 w-4 text-muted-foreground" />
        <span className="text-muted-foreground">人机验证</span>
        {isVerified && (
          <CheckCircle2 className="h-4 w-4 text-green-500" />
        )}
      </div>

      {/* ALTCHA组件容器 - 固定高度避免布局变动 */}
      <div className="relative min-h-[80px]">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center p-4 border rounded-md bg-muted/20">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-sm text-muted-foreground">加载验证组件...</span>
          </div>
        )}
        
        {/* ALTCHA Web Component */}
        <altcha-widget
          ref={altchaRef}
          challengeurl={challengeUrl}
          strings={JSON.stringify({
            label: "请验证您是真人",
            verifying: "验证中...",
            verified: "验证成功",
            error: "验证失败，请重试"
          })}
          style={{
            display: isLoading ? 'none' : 'block',
            opacity: disabled ? '0.5' : '1',
            pointerEvents: disabled ? 'none' : 'auto'
          }}
        />
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive" className="text-sm">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <button
              type="button"
              onClick={resetCaptcha}
              className="text-xs underline hover:no-underline ml-2"
              disabled={disabled}
            >
              重试
            </button>
          </AlertDescription>
        </Alert>
      )}

      {/* 验证成功提示 */}
      {isVerified && !error && (
        <div className="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400">
          <CheckCircle2 className="h-4 w-4" />
          <span>验证通过</span>
        </div>
      )}
    </div>
  );
}

// 导出重置方法的Hook
export function useAltchaCaptcha() {
  const [captchaPayload, setCaptchaPayload] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState(false);

  const handleVerified = useCallback((payload: string) => {
    console.log('useAltchaCaptcha handleVerified 被调用:', payload);
    setCaptchaPayload(payload);
    setIsVerified(true);
    // 强制重新渲染
    console.log('Hook状态更新完成 - isVerified: true, payload:', payload);
  }, []);

  const handleError = useCallback(() => {
    console.log('useAltchaCaptcha handleError 被调用');
    setCaptchaPayload(null);
    setIsVerified(false);
    console.log('Hook状态重置完成 - isVerified: false');
  }, []);

  const reset = useCallback(() => {
    setCaptchaPayload(null);
    setIsVerified(false);
  }, []);

  return {
    captchaPayload,
    isVerified,
    handleVerified,
    handleError,
    reset
  };
}