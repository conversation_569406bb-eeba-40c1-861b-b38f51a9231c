/**
 * 认证状态跟踪工具
 * 用于调试认证状态变化和问题排查
 */

interface AuthStateSnapshot {
  timestamp: string;
  location: string;
  hookState: {
    isAuthenticated: boolean;
    user: string | null;
    isLoading: boolean;
  };
  localStorage: {
    hasToken: boolean;
    hasUser: boolean;
    tokenExpires: string | null;
  };
  serviceState: {
    isAuthenticated: boolean;
  };
}

class AuthStateTracker {
  private snapshots: AuthStateSnapshot[] = [];
  private maxSnapshots = 50;

  /**
   * 记录状态快照
   */
  recordSnapshot(
    location: string,
    hookState: { isAuthenticated: boolean; user: any; isLoading: boolean }
  ) {
    if (typeof window === "undefined") return;

    const snapshot: AuthStateSnapshot = {
      timestamp: new Date().toISOString(),
      location,
      hookState: {
        isAuthenticated: hookState.isAuthenticated,
        user: hookState.user?.username || null,
        isLoading: hookState.isLoading,
      },
      localStorage: {
        hasToken: !!localStorage.getItem("auth_token"),
        hasUser: !!localStorage.getItem("auth_user"),
        tokenExpires: localStorage.getItem("auth_expires"),
      },
      serviceState: {
        isAuthenticated: this.checkServiceAuth(),
      },
    };

    this.snapshots.push(snapshot);
    
    // 保持最大快照数量
    if (this.snapshots.length > this.maxSnapshots) {
      this.snapshots = this.snapshots.slice(-this.maxSnapshots);
    }

    console.log("📊 AuthStateTracker:", location, snapshot);
  }

  /**
   * 检查服务层认证状态
   */
  private checkServiceAuth(): boolean {
    if (typeof window === "undefined") return false;
    
    const token = localStorage.getItem("auth_token");
    const expires = localStorage.getItem("auth_expires");
    
    if (!token) return false;
    
    if (expires) {
      const expiresDate = new Date(expires);
      const now = new Date();
      if (expiresDate <= now) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 获取所有快照
   */
  getSnapshots(): AuthStateSnapshot[] {
    return [...this.snapshots];
  }

  /**
   * 获取最近的快照
   */
  getLatestSnapshot(): AuthStateSnapshot | null {
    return this.snapshots[this.snapshots.length - 1] || null;
  }

  /**
   * 清除所有快照
   */
  clearSnapshots() {
    this.snapshots = [];
  }

  /**
   * 检测状态不一致
   */
  detectInconsistencies(): string[] {
    const latest = this.getLatestSnapshot();
    if (!latest) return [];

    const issues: string[] = [];

    // 检查 hook 状态和 localStorage 的一致性
    if (latest.hookState.isAuthenticated && !latest.localStorage.hasToken) {
      issues.push("Hook认证状态为true但localStorage中无token");
    }

    if (!latest.hookState.isAuthenticated && latest.localStorage.hasToken) {
      issues.push("Hook认证状态为false但localStorage中有token");
    }

    if (latest.hookState.user && !latest.localStorage.hasUser) {
      issues.push("Hook有用户信息但localStorage中无用户缓存");
    }

    // 检查 hook 状态和服务层状态的一致性
    if (latest.hookState.isAuthenticated !== latest.serviceState.isAuthenticated) {
      issues.push(`Hook认证状态(${latest.hookState.isAuthenticated})与服务层状态(${latest.serviceState.isAuthenticated})不一致`);
    }

    return issues;
  }

  /**
   * 生成状态报告
   */
  generateReport(): string {
    const latest = this.getLatestSnapshot();
    const issues = this.detectInconsistencies();
    
    let report = "=== 认证状态报告 ===\n";
    
    if (latest) {
      report += `时间: ${new Date(latest.timestamp).toLocaleString()}\n`;
      report += `位置: ${latest.location}\n`;
      report += `Hook状态: 认证=${latest.hookState.isAuthenticated}, 用户=${latest.hookState.user}, 加载=${latest.hookState.isLoading}\n`;
      report += `存储状态: Token=${latest.localStorage.hasToken}, 用户=${latest.localStorage.hasUser}\n`;
      report += `服务状态: 认证=${latest.serviceState.isAuthenticated}\n`;
    }
    
    if (issues.length > 0) {
      report += "\n=== 发现的问题 ===\n";
      issues.forEach((issue, index) => {
        report += `${index + 1}. ${issue}\n`;
      });
    } else {
      report += "\n✅ 未发现状态不一致问题\n";
    }
    
    return report;
  }
}

// 创建全局实例
export const authStateTracker = new AuthStateTracker();

// 在开发环境下暴露到全局对象，便于调试
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  (window as any).authStateTracker = authStateTracker;
}
