/** @type {import('next').NextConfig} */
const nextConfig = {
  // 优化性能配置
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@heroicons/react'],
  },
  
  // 优化webpack配置
  webpack: (config, { dev, isServer }) => {
    // 优化构建性能
    if (!isServer) {
      // 减少worker数量避免Jest worker错误
      config.parallelism = 1;
      
      // 优化代码分割
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // 核心库打包
            framework: {
              name: 'framework',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
              priority: 40,
              enforce: true,
            },
            // UI库打包
            lib: {
              name: 'lib',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](@heroicons|tailwindcss)[\\/]/,
              priority: 30,
              enforce: true,
            },
            // 应用代码打包
            commons: {
              name: 'commons',
              chunks: 'all',
              test: /[\\/]src[\\/](components|services|utils)[\\/]/,
              priority: 20,
              minChunks: 2,
              enforce: true,
            },
          },
        },
      };
    }

    // 减少内存使用
    config.optimization.minimize = !dev;
    
    return config;
  },

  // 开发服务器优化
  devIndicators: {
    position: 'bottom-right',
  },

  // API代理配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999'}/api/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
