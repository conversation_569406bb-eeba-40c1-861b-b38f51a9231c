"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  RefreshCw,
  Wifi,
  Server,
  Shield,
  Settings
} from "lucide-react";
import { runFullDiagnostics, type DiagnosticResult } from "@/utils/apiDiagnostics";
import { testApiConnection } from "@/utils/apiClient";

interface DiagnosticResults {
  networkStatus: DiagnosticResult;
  environmentConfig: DiagnosticResult;
  apiConnection: DiagnosticResult;
  authEndpoints: DiagnosticResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    criticalIssues: string[];
  };
}

export default function ApiDiagnosticsPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<DiagnosticResults | null>(null);

  const runDiagnostics = async () => {
    setLoading(true);
    try {
      const diagnosticResults = await runFullDiagnostics();
      setResults(diagnosticResults);
    } catch (error) {
      console.error('诊断失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    try {
      const result = await testApiConnection();
      console.log('连接测试结果:', result);
    } catch (error) {
      console.error('连接测试失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderDiagnosticResult = (result: DiagnosticResult, icon: React.ReactNode) => (
    <div className="flex items-start space-x-3 p-4 border rounded-lg">
      <div className="flex-shrink-0 mt-1">
        {result.success ? (
          <CheckCircle className="h-5 w-5 text-green-600" />
        ) : (
          <AlertCircle className="h-5 w-5 text-red-600" />
        )}
      </div>
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-2">
          {icon}
          <h3 className="font-medium">{result.message}</h3>
        </div>
        {result.details && (
          <div className="text-sm text-muted-foreground">
            <pre className="bg-gray-50 dark:bg-gray-800 p-2 rounded text-xs overflow-auto">
              {JSON.stringify(result.details, null, 2)}
            </pre>
          </div>
        )}
        {result.error && (
          <div className="text-sm text-red-600 mt-2">
            错误: {result.error}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <PageContainer>
      <div className="py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-display font-bold">API连接诊断</h1>
          <p className="text-muted-foreground mt-2">
            诊断和修复&ldquo;Failed to fetch&rdquo;等API连接问题
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wifi className="h-5 w-5" />
                <span>快速测试</span>
              </CardTitle>
              <CardDescription>
                快速测试API基础连接
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testConnection}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  <>
                    <Wifi className="h-4 w-4 mr-2" />
                    测试连接
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5" />
                <span>完整诊断</span>
              </CardTitle>
              <CardDescription>
                运行完整的API诊断测试
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={runDiagnostics}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    诊断中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    开始诊断
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>配置信息</span>
              </CardTitle>
              <CardDescription>
                查看当前API配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <div>
                  <strong>API URL:</strong>
                  <br />
                  <code className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}
                  </code>
                </div>
                <div>
                  <strong>环境:</strong> {process.env.NODE_ENV}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {results && (
          <div className="space-y-6">
            {/* 诊断总结 */}
            <Card>
              <CardHeader>
                <CardTitle>诊断总结</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {results.summary.passedTests}
                    </div>
                    <div className="text-sm text-muted-foreground">通过测试</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {results.summary.failedTests}
                    </div>
                    <div className="text-sm text-muted-foreground">失败测试</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {results.summary.totalTests}
                    </div>
                    <div className="text-sm text-muted-foreground">总测试数</div>
                  </div>
                </div>

                {results.summary.criticalIssues.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>关键问题:</strong>
                      <ul className="list-disc list-inside mt-2">
                        {results.summary.criticalIssues.map((issue, index) => (
                          <li key={index}>{issue}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* 详细结果 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>基础检查</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderDiagnosticResult(results.networkStatus, <Wifi className="h-4 w-4" />)}
                  {renderDiagnosticResult(results.environmentConfig, <Settings className="h-4 w-4" />)}
                  {renderDiagnosticResult(results.apiConnection, <Server className="h-4 w-4" />)}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>认证端点测试</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {results.authEndpoints.map((result, index) => (
                    <div key={index}>
                      {renderDiagnosticResult(result, <Shield className="h-4 w-4" />)}
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* 故障排除指南 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>故障排除指南</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">常见问题及解决方案：</h4>
                <ul className="space-y-2 list-disc list-inside text-muted-foreground">
                  <li><strong>Failed to fetch:</strong> 检查API服务器是否运行，确认URL配置正确</li>
                  <li><strong>CORS错误:</strong> 确保后端配置了正确的CORS策略</li>
                  <li><strong>网络超时:</strong> 检查网络连接，或增加请求超时时间</li>
                  <li><strong>401未授权:</strong> 检查token是否有效，可能需要重新登录</li>
                  <li><strong>500服务器错误:</strong> 检查后端服务器日志</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">环境配置检查：</h4>
                <ul className="space-y-2 list-disc list-inside text-muted-foreground">
                  <li>确认 <code>NEXT_PUBLIC_API_BASE_URL</code> 环境变量设置正确</li>
                  <li>检查 <code>.env.local</code> 文件是否存在且配置正确</li>
                  <li>重启开发服务器以应用环境变量更改</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
