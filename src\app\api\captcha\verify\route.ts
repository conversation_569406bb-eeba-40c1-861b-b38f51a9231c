import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { payload } = body;

    if (!payload) {
      return NextResponse.json(
        { 
          error: 'Missing payload',
          message: '缺少验证负载' 
        },
        { status: 400 }
      );
    }

    // 获取API基础URL
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999';
    
    // 构建后端API URL
    const backendUrl = `${apiBaseUrl}/api/captcha/verify`;

    // 转发请求到后端
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 转发客户端IP和User-Agent
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
        'User-Agent': request.headers.get('user-agent') || '',
      },
      body: JSON.stringify({ payload }),
    });

    if (!response.ok) {
      console.error('Backend verify API error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          verified: false,
          error: 'Verification failed',
          message: '验证失败，请重试' 
        },
        { status: response.status }
      );
    }

    const verifyResult = await response.json();

    // 返回验证结果
    return NextResponse.json(verifyResult, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, max-age=0',
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('Captcha verification error:', error);
    return NextResponse.json(
      { 
        verified: false,
        error: 'Internal server error',
        message: '服务器内部错误，请稍后重试' 
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}