"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

// 路径映射配置
const pathMap: Record<string, string> = {
  "/": "首页",
  "/auth": "认证",
  "/auth/login": "登录",
  "/auth/register": "注册",
  "/auth/forgot-password": "忘记密码",
  "/auth/reset-password": "重置密码",
  "/auth/verify-email": "邮箱验证",
  "/profile": "个人资料",
  "/profile/edit": "编辑资料",
  "/profile/points": "积分历史",
  "/profile/help-requests": "我的求助",
  "/profile/help-answers": "我的回答",
  "/help-requests": "求助中心",
  "/admin": "管理后台",
  "/search": "搜索",
  "/tutorials": "教程",
};

export function Breadcrumb({ 
  items, 
  className, 
  showHome = true 
}: BreadcrumbProps) {
  const pathname = usePathname();
  
  // 如果提供了自定义items，使用自定义items
  if (items) {
    return (
      <nav className={cn("flex items-center space-x-1 text-sm", className)}>
        {showHome && (
          <>
            <Link
              href="/"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            >
              <Home className="h-4 w-4" />
            </Link>
            {items.length > 0 && (
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            )}
          </>
        )}
        
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {item.href ? (
              <Link
                href={item.href}
                className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
              >
                {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                {item.label}
              </Link>
            ) : (
              <span className="flex items-center text-foreground font-medium">
                {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                {item.label}
              </span>
            )}
            
            {index < items.length - 1 && (
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            )}
          </React.Fragment>
        ))}
      </nav>
    );
  }

  // 自动生成面包屑
  const pathSegments = pathname?.split("/").filter(Boolean) || [];
  const breadcrumbItems: BreadcrumbItem[] = [];

  // 构建面包屑路径
  let currentPath = "";
  for (const segment of pathSegments) {
    currentPath += `/${segment}`;
    const label = pathMap[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1);
    breadcrumbItems.push({
      label,
      href: currentPath,
    });
  }

  // 最后一项不应该是链接
  if (breadcrumbItems.length > 0) {
    breadcrumbItems[breadcrumbItems.length - 1].href = undefined;
  }

  // 如果只有一个项目且是首页，不显示面包屑
  if (breadcrumbItems.length === 0 || (breadcrumbItems.length === 1 && pathname === "/")) {
    return null;
  }

  return (
    <nav className={cn("flex items-center space-x-1 text-sm", className)}>
      {showHome && (
        <>
          <Link
            href="/"
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <Home className="h-4 w-4" />
          </Link>
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
        </>
      )}
      
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {item.href ? (
            <Link
              href={item.href}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-foreground font-medium">
              {item.label}
            </span>
          )}
          
          {index < breadcrumbItems.length - 1 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

// 便捷的面包屑组件，用于特定页面
export function ProfileBreadcrumb() {
  return (
    <Breadcrumb
      items={[
        { label: "个人资料", href: "/profile" },
      ]}
    />
  );
}

export function AuthBreadcrumb({ currentPage }: { currentPage: string }) {
  return (
    <Breadcrumb
      items={[
        { label: "认证", href: "/auth" },
        { label: currentPage },
      ]}
    />
  );
}
