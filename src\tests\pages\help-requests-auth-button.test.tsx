/**
 * 资源求助页面认证按钮测试
 * 验证未登录用户也能看到发布求助按钮，点击后跳转到登录页面
 */

import React from "react";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";

// Mock next/navigation
const mockPush = vi.fn();
const mockRouter = {
  push: mockPush,
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  replace: vi.fn(),
};

vi.mock("next/navigation", () => ({
  useRouter: () => mockRouter,
}));

// Mock ToastProvider
vi.mock("../../components/ToastProvider", () => ({
  useToast: () => ({
    showToast: vi.fn(),
  }),
}));

// Mock authService
vi.mock("../../services/authService", () => ({
  getCurrentUser: vi.fn().mockResolvedValue(null),
  isAuthenticated: vi.fn().mockReturnValue(false),
  logout: vi.fn(),
}));

// Mock help request service
vi.mock("../../services/helpRequestService", () => ({
  getHelpRequests: vi.fn().mockResolvedValue({
    status: "success",
    data: { requests: [], pages: 1, total: 0 },
  }),
}));

// Mock useAuth hook
const mockRequireAuth = vi.fn();
vi.mock("../../hooks/useAuth", () => ({
  useAuth: () => ({
    isAuthenticated: false, // 模拟未登录状态
    requireAuth: mockRequireAuth,
    user: null,
    isLoading: false,
  }),
}));

// Mock HelpRequestFilters component
vi.mock("../../components/help-requests/HelpRequestFilters", () => ({
  default: ({ filters, onFiltersChange }: any) => (
    <div data-testid="help-request-filters">Filters Component</div>
  ),
}));

// 动态导入页面组件
async function getHelpRequestsPage() {
  const module = await import("../../app/help-requests/page");
  return module.default;
}

describe("资源求助页面 - 未登录用户发布求助按钮", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该为未登录用户显示发布求助按钮", async () => {
    const HelpRequestsPage = await getHelpRequestsPage();
    render(<HelpRequestsPage />);

    // 应该能找到发布求助按钮
    const createButtons = screen.getAllByText("发布求助");
    expect(createButtons.length).toBeGreaterThan(0);

    // 检查按钮是否为 button 元素（未登录用户）
    const headerButton = createButtons[0];
    expect(headerButton.tagName).toBe("BUTTON");
  });

  it("应该在点击发布求助按钮时调用 requireAuth", async () => {
    const HelpRequestsPage = await getHelpRequestsPage();
    render(<HelpRequestsPage />);

    // 点击发布求助按钮
    const createButton = screen.getAllByText("发布求助")[0];
    fireEvent.click(createButton);

    // 应该调用 requireAuth 并传入正确的重定向路径
    expect(mockRequireAuth).toHaveBeenCalledWith("/help-requests/create");
  });

  it("应该在暂无求助信息时显示发布第一个求助按钮", async () => {
    const HelpRequestsPage = await getHelpRequestsPage();
    render(<HelpRequestsPage />);

    // 等待组件加载完成
    await screen.findByText("暂无求助信息");

    // 应该能找到发布第一个求助按钮
    const createFirstButton = screen.getByText("发布第一个求助");
    expect(createFirstButton).toBeInTheDocument();
    expect(createFirstButton.tagName).toBe("BUTTON");
  });

  it("应该在点击发布第一个求助按钮时调用 requireAuth", async () => {
    const HelpRequestsPage = await getHelpRequestsPage();
    render(<HelpRequestsPage />);

    // 等待组件加载完成
    await screen.findByText("暂无求助信息");

    // 点击发布第一个求助按钮
    const createFirstButton = screen.getByText("发布第一个求助");
    fireEvent.click(createFirstButton);

    // 应该调用 requireAuth 并传入正确的重定向路径
    expect(mockRequireAuth).toHaveBeenCalledWith("/help-requests/create");
  });
});

// 测试已登录用户的情况
describe("资源求助页面 - 已登录用户发布求助按钮", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock 已登录状态
    vi.doMock("../../hooks/useAuth", () => ({
      useAuth: () => ({
        isAuthenticated: true, // 模拟已登录状态
        requireAuth: mockRequireAuth,
        user: { id: 1, username: "testuser" },
        isLoading: false,
      }),
    }));
  });

  it("应该为已登录用户显示链接形式的发布求助按钮", async () => {
    const HelpRequestsPage = await getHelpRequestsPage();
    render(<HelpRequestsPage />);

    // 应该能找到发布求助按钮
    const createButtons = screen.getAllByText("发布求助");
    expect(createButtons.length).toBeGreaterThan(0);

    // 检查按钮是否为 a 元素（已登录用户）
    const headerButton = createButtons[0];
    expect(headerButton.tagName).toBe("A");
    expect(headerButton).toHaveAttribute("href", "/help-requests/create");
  });
});
