import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 获取API基础URL
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999';
    
    // 构建后端API URL
    const backendUrl = `${apiBaseUrl}/api/auth/altcha-challenge`;

    // 转发请求到后端
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // 转发客户端IP
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
      },
    });

    if (!response.ok) {
      console.error('Backend challenge API error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to generate challenge',
          message: '验证码生成失败，请稍后重试' 
        },
        { status: 500 }
      );
    }

    const challengeData = await response.json();

    // 返回挑战数据
    return NextResponse.json(challengeData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, max-age=0',
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('Challenge generation error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: '服务器内部错误，请稍后重试' 
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}