"use client";

import Link from 'next/link';

interface ErrorPageClientProps {
  isNotFound: boolean;
  message: string;
}

export default function ErrorPageClient({ isNotFound, message }: ErrorPageClientProps) {
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="text-center py-12">
        {/* 错误图标 */}
        <div className="mx-auto w-16 h-16 mb-4 text-gray-400">
          {isNotFound ? (
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.219 0-4.239-.928-5.678-2.416A7.962 7.962 0 004 12c0-4.418 3.582-8 8-8s8 3.582 8 8a7.962 7.962 0 01-2.322 5.291z" />
            </svg>
          ) : (
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
        </div>
        
        {/* 错误信息 */}
        <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {isNotFound ? "求助不存在" : "加载失败"}
        </h1>
        <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
          {message || (isNotFound ? "您访问的求助可能已被删除或链接有误" : "服务器暂时不可用，请稍后重试")}
        </p>
        
        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link
            href="/help-requests"
            className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            返回求助列表
          </Link>
          {!isNotFound && (
            <button
              onClick={handleReload}
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重新加载
            </button>
          )}
        </div>
      </div>
    </div>
  );
}