"use client";

import React from "react";
import { UserBasicInfo } from "@/types/help-request";

interface AvatarProps {
  user: UserBasicInfo;
  size?: "sm" | "md" | "lg";
  showName?: boolean;
  className?: string;
}

const AVATAR_SIZES = {
  sm: "w-8 h-8",
  md: "w-10 h-10",
  lg: "w-12 h-12",
} as const;

const TEXT_SIZES = {
  sm: "text-xs",
  md: "text-xs",
  lg: "text-sm",
} as const;

export default function Avatar({ 
  user, 
  size = "md", 
  showName = true, 
  className = "" 
}: AvatarProps) {
  const displayName = user.nickname || user.username;
  const initial = displayName.charAt(0).toUpperCase();
  
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
    if (target.nextElementSibling) {
      (target.nextElementSibling as HTMLElement).style.display = 'flex';
    }
  };

  return (
    <div className={`flex-shrink-0 flex flex-col items-center ${className}`}>
      <div className={`${AVATAR_SIZES[size]} rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-600`}>
        {user.avatar_url ? (
          <img
            src={user.avatar_url}
            alt={`${displayName}的头像`}
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
        ) : null}
        <div 
          className={`w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400 ${TEXT_SIZES[size]} font-medium ${user.avatar_url ? 'hidden' : ''}`}
        >
          {initial}
        </div>
      </div>
      {showName && (
        <div className="mt-1 text-[10px] text-gray-500 dark:text-gray-400 text-center max-w-[50px] truncate leading-tight">
          {user.nickname || user.username || user.title}
        </div>
      )}
    </div>
  );
}