import { NextResponse } from 'next/server';

/**
 * 资源统计API - 代理到后端
 */
export async function GET() {
  const backendUrl = process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999';

  try {
    const response = await fetch(`${backendUrl}/api/resource_stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Proxy',
      },
    });

    if (!response.ok) {
      console.error(`后端resource_stats API错误: ${response.status}`);
      // 返回默认值避免前端错误
      return NextResponse.json({
        status: 'error',
        message: '资源统计暂时不可用',
        total: 0,
        yesterday: 0
      });
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('resource_stats API代理错误:', error);
    // 返回默认值避免前端错误
    return NextResponse.json({
      status: 'error',
      message: '资源统计暂时不可用',
      total: 0,
      yesterday: 0
    });
  }
}