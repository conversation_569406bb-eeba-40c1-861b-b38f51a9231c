"use client";

import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  UserIcon,
  Cog6ToothIcon,
  StarIcon,
  ArrowRightStartOnRectangleIcon,
  QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/Button";

interface PersonalMenuProps {
  className?: string;
}

/**
 * 个人菜单下拉列表组件
 * 根据用户登录状态显示不同的菜单内容
 */
export default function PersonalMenu({ className = "" }: PersonalMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const router = useRouter();
  const { user, isAuthenticated, isLoading, logout, isAdmin } = useAuth();

  // 调试信息：跟踪认证状态变化
  useEffect(() => {
    console.log("🔐 PersonalMenu状态变化:", {
      isAuthenticated,
      user: user?.username,
      hasUser: !!user,
      isLoading,
      shouldShowLoginButton: !isAuthenticated || !user,
    });
  }, [isAuthenticated, user, isLoading]);

  // 认证状态变化时关闭菜单
  useEffect(() => {
    if (isOpen) {
      setIsOpen(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]); // 当认证状态变化时关闭菜单，忽略isOpen依赖

  // 强制重新渲染以确保状态同步
  useEffect(() => {
    // 当用户状态或认证状态发生变化时，强制组件重新评估显示逻辑
    console.log("🔐 PersonalMenu强制重新评估:", {
      isAuthenticated,
      hasUser: !!user,
      isLoading,
      timestamp: Date.now(),
    });
  }, [isAuthenticated, user, isLoading]);

  // 切换菜单显示状态
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // 关闭菜单
  const closeMenu = () => {
    setIsOpen(false);
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      closeMenu();
      router.push("/");
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  // 处理菜单项点击
  const handleMenuItemClick = () => {
    closeMenu();
  };

  // 点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        closeMenu();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // ESC键关闭菜单
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        closeMenu();
      }
    };

    document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen]);

  return (
    <div className={`relative ${className}`}>
      {/* 触发按钮 */}
      <button
        ref={buttonRef}
        type="button"
        onClick={toggleMenu}
        className="flex items-center justify-center p-2 rounded-full hover:bg-hover-background transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="个人菜单"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <UserIcon className="h-5 w-5 text-foreground" />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div
          ref={menuRef}
          className="absolute right-0 mt-2 w-48 glass-effect border border-border-color rounded-lg shadow-lg z-50 animate-in fade-in-0 zoom-in-95 duration-200
                     sm:right-0 max-w-[calc(100vw-2rem)] sm:max-w-none"
          role="menu"
          aria-orientation="vertical"
        >
          {isLoading ? (
            // 加载状态
            <div className="p-4 text-center">
              <div className="text-sm text-secondary-text">加载中...</div>
            </div>
          ) : isAuthenticated && user ? (
            // 已登录用户菜单
            <div className="py-1">
              {/* 用户信息区域 */}
              <div className="px-4 py-3 border-b border-border-color">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <UserIcon className="h-4 w-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {user?.username || "用户"}
                    </p>
                    {user?.email && (
                      <p className="text-xs text-secondary-text truncate">
                        {user.email}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* 功能菜单项 */}
              <div className="py-1">
                <Link
                  href="/profile"
                  className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                  role="menuitem"
                  onClick={handleMenuItemClick}
                >
                  <UserIcon className="h-4 w-4 mr-3 text-secondary-text" />
                  个人资料
                </Link>

                <Link
                  href="/profile/edit"
                  className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors whitespace-nowrap"
                  role="menuitem"
                  onClick={handleMenuItemClick}
                >
                  <Cog6ToothIcon className="h-4 w-4 mr-3 text-secondary-text" />
                  编辑资料
                </Link>

                <Link
                  href="/profile/points"
                  className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                  role="menuitem"
                  onClick={handleMenuItemClick}
                >
                  <StarIcon className="h-4 w-4 mr-3 text-secondary-text" />
                  积分历史
                </Link>

                <Link
                  href="/profile/help-requests"
                  className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                  role="menuitem"
                  onClick={handleMenuItemClick}
                >
                  <QuestionMarkCircleIcon className="h-4 w-4 mr-3 text-secondary-text" />
                  我的求助
                </Link>

                {/* 管理员菜单项 */}
                {isAdmin() && (
                  <Link
                    href="/admin"
                    className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                    role="menuitem"
                    onClick={handleMenuItemClick}
                  >
                    <Cog6ToothIcon className="h-4 w-4 mr-3 text-secondary-text" />
                    管理后台
                  </Link>
                )}
              </div>

              {/* 登出按钮 */}
              <div className="border-t border-border-color pt-1">
                <button
                  type="button"
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                  role="menuitem"
                >
                  <ArrowRightStartOnRectangleIcon className="h-4 w-4 mr-3 text-secondary-text" />
                  退出登录
                </button>
              </div>
            </div>
          ) : (
            // 未登录用户菜单 - 仅显示登录按钮
            <div className="p-3">
              <Link
                href="/auth/login"
                className="block w-full"
                onClick={handleMenuItemClick}
              >
                <Button variant="default" size="sm" className="w-full">
                  登录
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
