import { NextResponse } from 'next/server';

/**
 * 测试API代理的路由
 */
export async function GET() {
  try {
    // 获取后端API的URL
    const backendUrl = process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999';
    const testUrl = `${backendUrl}/api/test`;

    console.log('🔗 测试代理连接到:', testUrl);

    // 尝试连接到后端API
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json({
        success: true,
        message: '代理连接成功',
        backend_response: data,
        backend_url: testUrl,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: '后端服务器响应错误',
        status: response.status,
        statusText: response.statusText,
        backend_url: testUrl,
      }, { status: 502 });
    }
  } catch (error) {
    console.error('代理测试失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '无法连接到后端服务器',
      error: error instanceof Error ? error.message : '未知错误',
      backend_url: process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999',
      suggestion: '请确保后端服务器正在运行',
    }, { status: 503 });
  }
}
