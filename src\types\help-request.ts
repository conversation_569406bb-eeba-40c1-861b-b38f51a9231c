/**
 * 资源求助相关的类型定义
 */

// 求助状态枚举
export type HelpRequestStatus = "open" | "resolved" | "closed";

// 网盘类型映射
export const PAN_TYPE_MAP = {
  1: "百度网盘",
  2: "夸克网盘",
  3: "阿里云盘",
  4: "迅雷网盘",
} as const;

// 资源类型选项（匹配后端API规范）
export const RESOURCE_TYPES = [
  { value: "movie", label: "电影" },
  { value: "tv_series", label: "电视剧" },
  { value: "software", label: "软件" },
  { value: "game", label: "游戏" },
  { value: "music", label: "音乐" },
  { value: "book", label: "书籍" },
  { value: "document", label: "文档" },
  { value: "other", label: "其他" },
] as const;

// 网盘类型字符串映射
export const CLOUD_DISK_TYPES = [
  { value: "baidu", label: "百度网盘" },
  { value: "quark", label: "夸克网盘" },
  { value: "aliyun", label: "阿里云盘" },
  { value: "xunlei", label: "迅雷网盘" },
] as const;

// 用户基础信息
export interface UserBasicInfo {
  id: number;
  username: string;
  nickname?: string;
  points: number;
  title?: string;
  avatar?: string;
  avatar_url?: string; // 新增头像URL字段
}

// 求助主体
export interface HelpRequest {
  id: number;
  title: string; // 资源名称（必填）
  description?: string; // 资源描述（可选）
  cloud_disk_types: string[]; // 网盘类型（必填，支持多选）
  resource_type: string; // 资源类型
  status: HelpRequestStatus;
  requester: UserBasicInfo; // 求助者信息
  answer_count: number; // 回答数量
  view_count: number; // 浏览次数
  created_at: string;
  updated_at: string;
  resolved_at?: string; // 解决时间
  tags?: string[]; // 标签
}

// 求助回答
export interface HelpAnswer {
  id: number;
  help_request_id: number;
  user_id: number;
  user: UserBasicInfo;
  resource_link: string; // 资源链接（必填）
  pan_type: number; // 网盘类型（必填）
  description?: string; // 资源描述（可选）
  is_parsed: boolean; // 是否解析
  is_best: boolean; // 是否为最佳答案
  created_at: string;
  updated_at: string;
}

// 求助筛选条件
export interface HelpRequestFilters {
  status?: "all" | HelpRequestStatus;
  pan_type?: number;
  resource_type?: string;
  search?: string; // 搜索关键词（标题和描述）
  user_id?: number; // 用于筛选特定用户的求助
}

// 创建求助的数据（匹配后端API规范）
export interface CreateHelpRequestData {
  title: string; // 资源名称，1-200字符
  description?: string | null; // 资源描述，可选
  cloud_disk_types: string[]; // 网盘类型列表，至少1项
  resource_type: "movie" | "tv_series" | "software" | "game" | "music" | "book" | "document" | "other"; // 资源类型
}

// 创建回答的数据
export interface CreateAnswerData {
  help_request_id: number;
  resource_link: string;
  pan_type: number;
  description?: string;
  is_parsed: boolean;
}

// API响应类型
export interface HelpRequestListResponse {
  status: "success" | "error";
  message: string;
  data: {
    total: number;
    page: number;
    size: number;
    pages: number;
    requests: HelpRequest[];
  };
  error?: string;
}

export interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

// 管理员相关类型
export interface AdminHelpRequestFilters extends HelpRequestFilters {
  search?: string; // 搜索关键词
  sort_by?: "latest" | "oldest"; // 排序方式
}

export interface AdminHelpRequestListResponse {
  success: boolean;
  data: {
    help_requests: (HelpRequest & {
      user_email?: string; // 管理员可以看到用户邮箱
    })[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

// 求助统计信息
export interface HelpRequestStats {
  total_requests: number;
  open_requests: number;
  solved_requests: number;
  closed_requests: number;
  total_answers: number;
  today_requests: number;
  week_requests: number;
}

// 求助回答（详情页面使用）
export interface HelpRequestAnswer {
  id: number;
  resource_title: string; // 资源标题
  resource_link: string; // 资源链接
  cloud_disk_type: string; // 网盘类型
  additional_info?: string; // 附加信息
  should_archive: boolean; // 是否应该归档
  answerer: UserBasicInfo; // 回答者信息
  is_accepted: boolean; // 是否被采纳
  accepted_at?: string; // 采纳时间
  created_at: string;
  updated_at?: string;
}

// 求助详情
export interface HelpRequestDetail extends HelpRequest {
  answers: HelpRequestAnswer[]; // 回答列表
  can_answer?: boolean | null; // 是否可以回答
  can_accept?: boolean | null; // 是否可以采纳回答
}

// 求助详情API响应
export interface HelpRequestDetailResponse {
  status: "success" | "error";
  message: string;
  data: HelpRequestDetail | null;
  error?: string;
}
