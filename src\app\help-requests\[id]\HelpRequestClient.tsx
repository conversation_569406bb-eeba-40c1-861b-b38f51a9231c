"use client";

import { useState, useCallback, useMemo } from "react";
import { DocumentDuplicateIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import {
  type HelpRequestDetail,
  type HelpRequestAnswer,
} from "@/types/help-request";
import ReplyForm from "@/components/help-requests/ReplyForm";
import Avatar from "@/components/help-requests/Avatar";
import { getHelpRequestDetail } from "@/services/helpRequestService";
import React from "react";

// 格式化时间 - 使用useMemo优化
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 回答卡片组件 - 优化性能
const AnswerCard = React.memo(({ answer }: { answer: HelpRequestAnswer }) => {
  const { showToast } = useToast();
  
  // 复制资源链接到剪贴板 - 使用useCallback优化
  const copyResourceLink = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(answer.resource_link);
      showToast("资源链接已复制到剪贴板", "success");
    } catch (error) {
      console.error("复制失败:", error);
      showToast("复制失败，请手动复制", "error");
    }
  }, [answer.resource_link, showToast]);

  // 网盘类型显示文本 - 使用useMemo优化
  const diskTypeText = useMemo(() => {
    const typeMap: Record<string, string> = {
      baidu: "百度网盘",
      aliyun: "阿里云盘", 
      quark: "夸克网盘",
      xunlei: "迅雷网盘"
    };
    return typeMap[answer.cloud_disk_type] || answer.cloud_disk_type;
  }, [answer.cloud_disk_type]);

  // 用户显示名称 - 使用useMemo优化
  const userDisplayName = useMemo(() => {
    return answer.answerer.nickname || answer.answerer.username;
  }, [answer.answerer.nickname, answer.answerer.username]);

  // 格式化时间 - 使用useMemo优化
  const formattedTime = useMemo(() => {
    return formatDateTime(answer.created_at);
  }, [answer.created_at]);

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-sm">
      <div className="p-4">
        {/* 资源标题 - 去除字段名称 */}
        <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 line-clamp-2 leading-snug">
          {answer.resource_title}
        </h4>

        {/* 网盘类型标签 */}
        <div className="flex items-center space-x-1.5 mb-4">
          <span className="px-2 py-1 bg-blue-500 text-white rounded-full text-xs font-medium">
            {diskTypeText}
          </span>
        </div>

        {/* 用户信息和统计信息行 */}
        <div className="flex items-center justify-between mb-4">
          {/* 用户头像和昵称 */}
          <div className="flex items-center gap-2">
            <Avatar user={answer.answerer} size="sm" showName={false} />
            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {userDisplayName}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formattedTime}
              </span>
            </div>
          </div>

          {/* 右侧状态和积分 */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {answer.answerer.points} 积分
            </span>
            {answer.is_accepted && (
              <div className="flex items-center space-x-1 bg-orange-500 px-2 py-1 rounded-full">
                <CheckCircleIcon className="w-3 h-3 text-white" />
                <span className="text-white text-xs font-medium">
                  已采纳
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 资源链接 - 移动端优化，添加复制按钮 */}
        <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-3 border border-gray-200 dark:border-gray-700 mb-3">
          <div className="flex items-center justify-between gap-3">
            <p className="text-sm text-gray-700 dark:text-gray-300 break-all flex-1">
              {answer.resource_link}
            </p>
            <button
              onClick={copyResourceLink}
              className="flex-shrink-0 p-1.5 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
              title="复制链接"
              type="button"
            >
              <DocumentDuplicateIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 附加信息 - 去除字段名称 */}
        {answer.additional_info && (
          <div className="border-t border-gray-100 dark:border-gray-700 pt-3">
            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              {answer.additional_info}
            </p>
          </div>
        )}
      </div>
    </div>
  );
});

AnswerCard.displayName = 'AnswerCard';

interface HelpRequestClientProps {
  initialData: HelpRequestDetail;
  requestId: string;
}

const HelpRequestClient = React.memo(({ initialData, requestId }: HelpRequestClientProps) => {
  const [helpRequest, setHelpRequest] = useState<HelpRequestDetail>(initialData);
  const { showToast } = useToast();

  // 重新加载求助详情 - 使用useCallback优化
  const handleReplySuccess = useCallback(async () => {
    try {
      const response = await getHelpRequestDetail(requestId);
      if (response.status === "success" && response.data) {
        setHelpRequest(response.data);
      } else {
        showToast(response.message || "刷新数据失败", "error");
      }
    } catch (error) {
      console.error("刷新数据失败:", error);
      showToast("刷新数据失败", "error");
    }
  }, [requestId, showToast]);

  // 资源类型显示文本 - 使用useMemo优化
  const resourceTypeText = useMemo(() => {
    const typeMap: Record<string, string> = {
      movie: "电影",
      tv: "电视剧", 
      music: "音乐",
      software: "软件",
      game: "游戏",
      book: "书籍",
      document: "文档",
      other: "其他"
    };
    return helpRequest.resource_type ? typeMap[helpRequest.resource_type] || helpRequest.resource_type : null;
  }, [helpRequest.resource_type]);

  // 网盘类型标签 - 使用useMemo优化
  const diskTypeTags = useMemo(() => {
    const typeMap: Record<string, string> = {
      baidu: "百度网盘",
      aliyun: "阿里云盘",
      quark: "夸克网盘", 
      xunlei: "迅雷网盘"
    };
    return helpRequest.cloud_disk_types?.map(diskType => ({
      key: diskType,
      text: typeMap[diskType] || diskType
    })) || [];
  }, [helpRequest.cloud_disk_types]);

  // 求助者显示名称 - 使用useMemo优化
  const requesterDisplayName = useMemo(() => {
    return helpRequest.requester.nickname || helpRequest.requester.username;
  }, [helpRequest.requester.nickname, helpRequest.requester.username]);

  // 格式化创建时间 - 使用useMemo优化
  const formattedCreatedTime = useMemo(() => {
    return formatDateTime(helpRequest.created_at);
  }, [helpRequest.created_at]);

  // 是否显示回复表单 - 使用useMemo优化
  const showReplyForm = useMemo(() => {
    return helpRequest.status !== "resolved";
  }, [helpRequest.status]);

  return (
    <div className="max-w-4xl mx-auto px-4 py-4 md:py-6">
      {/* 求助详情卡片 - 移动端优化 */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl mb-6 shadow-sm">
        <div className="p-4 md:p-6">
          {/* 标题 */}
          <h1 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 line-clamp-2 leading-snug">
            {helpRequest.title}
          </h1>

          {/* 标签行 */}
          <div className="flex flex-wrap gap-2 mb-4">
            {resourceTypeText && (
              <span className="px-3 py-1 bg-purple-500 text-white rounded-full text-sm font-medium">
                {resourceTypeText}
              </span>
            )}
            {diskTypeTags.map(({ key, text }) => (
              <span
                key={key}
                className="px-3 py-1 bg-blue-500 text-white rounded-full text-sm font-medium"
              >
                {text}
              </span>
            ))}
          </div>

          {/* 用户信息和统计信息行 */}
          <div className="flex items-center justify-between mb-4">
            {/* 用户头像和昵称 */}
            <div className="flex items-center gap-3">
              <Avatar user={helpRequest.requester} size="md" showName={false} />
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {requesterDisplayName}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formattedCreatedTime}
                </span>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <span className="flex items-center gap-1">
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path
                    fillRule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clipRule="evenodd"
                  />
                </svg>
                {helpRequest.view_count}
              </span>
              <span className="flex items-center gap-1">
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                    clipRule="evenodd"
                  />
                </svg>
                {helpRequest.answer_count}
              </span>
            </div>
          </div>

          {/* 描述 */}
          {helpRequest.description && (
            <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-sm md:text-base">
                {helpRequest.description}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 回答列表 */}
      <div className="space-y-4">
        <h2 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-gray-100 px-1">
          最新回复 ({helpRequest.answers.length})
        </h2>

        {helpRequest.answers.length > 0 ? (
          <div className="space-y-4">
            {helpRequest.answers.map((answer) => (
              <AnswerCard key={answer.id} answer={answer} />
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl p-8 text-center shadow-sm">
            <p className="text-gray-500 dark:text-gray-400 text-sm">暂无回答</p>
          </div>
        )}
      </div>

      {/* 回复组件 - 仅在帖子未解决时显示 */}
      {showReplyForm && (
        <div className="mt-6">
          <ReplyForm 
            requestId={requestId} 
            onReplySuccess={handleReplySuccess}
          />
        </div>
      )}
    </div>
  );
});

HelpRequestClient.displayName = 'HelpRequestClient';

export default HelpRequestClient;