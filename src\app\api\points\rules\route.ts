import { NextResponse } from "next/server";

const BACKEND_API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export async function GET() {
  try {
    // 检查环境变量
    if (!BACKEND_API_URL) {
      return NextResponse.json(
        {
          success: false,
          message: "后端API地址未配置",
          error: "NEXT_PUBLIC_API_BASE_URL environment variable is not set",
        },
        { status: 500 }
      );
    }

    // 构建后端API URL
    const backendUrl = new URL(`${BACKEND_API_URL}/api/points/rules`);

    // 转发请求到后端服务
    const response = await fetch(backendUrl.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 获取后端响应数据
    const data = await response.json();

    // 如果后端返回错误状态码，保持相同的状态码
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // 返回成功响应
    return NextResponse.json(data);
  } catch (error) {
    console.error("获取积分规则失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器处理请求时出错",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
