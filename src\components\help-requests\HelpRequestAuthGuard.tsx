/**
 * 求助功能权限控制组件
 * 根据用户权限和操作类型控制访问
 */

import React from "react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { HelpRequest } from "@/types/help-request";

// 权限级别定义
export const HELP_REQUEST_PERMISSIONS = {
  VIEW: 'help_request:view',           // 查看求助（游客可访问）
  CREATE: 'help_request:create',       // 发布求助（需登录）
  ANSWER: 'help_request:answer',       // 回答求助（需登录）
  ADOPT: 'help_request:adopt',         // 采纳答案（求助者）
  DELETE: 'help_request:delete',       // 删除求助（管理员或求助者）
  MODERATE: 'help_request:moderate',   // 管理求助（管理员）
} as const;

type PermissionAction = keyof typeof HELP_REQUEST_PERMISSIONS;

interface HelpRequestAuthGuardProps {
  children: React.ReactNode;
  action: PermissionAction;
  helpRequest?: HelpRequest;
  fallback?: React.ReactNode;
  showLoginPrompt?: boolean;
}

// 登录提示组件
function LoginPrompt({ message = "请登录后继续操作" }: { message?: string }) {
  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 text-center">
      <p className="text-blue-800 dark:text-blue-200 mb-3">{message}</p>
      <Link
        href="/auth/login"
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        立即登录
      </Link>
    </div>
  );
}

// 无权限提示组件
function NoPermissionMessage({ message = "您没有权限执行此操作" }: { message?: string }) {
  return (
    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-center">
      <p className="text-red-800 dark:text-red-200">{message}</p>
    </div>
  );
}

export default function HelpRequestAuthGuard({
  children,
  action,
  helpRequest,
  fallback,
  showLoginPrompt = true,
}: HelpRequestAuthGuardProps) {
  const { isAuthenticated, user, isAdmin } = useAuth();

  // 查看权限：所有人都可以
  if (action === 'VIEW') {
    return <>{children}</>;
  }

  // 需要登录的操作
  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showLoginPrompt) {
      const messages = {
        CREATE: "请登录后发布求助",
        ANSWER: "请登录后回答求助",
        ADOPT: "请登录后采纳答案",
        DELETE: "请登录后删除求助",
        MODERATE: "请登录后管理求助",
      };
      return <LoginPrompt message={messages[action]} />;
    }
    
    return null;
  }

  // 采纳答案：只有求助者可以
  if (action === 'ADOPT' && helpRequest) {
    if (user?.id !== helpRequest.requester.id) {
      return fallback || <NoPermissionMessage message="只有求助者可以采纳答案" />;
    }
  }

  // 删除权限：管理员或求助者
  if (action === 'DELETE' && helpRequest) {
    if (!isAdmin() && user?.id !== helpRequest.requester.id) {
      return fallback || <NoPermissionMessage message="只有管理员或求助者可以删除求助" />;
    }
  }

  // 管理权限：只有管理员
  if (action === 'MODERATE') {
    if (!isAdmin()) {
      return fallback || <NoPermissionMessage message="只有管理员可以管理求助" />;
    }
  }

  return <>{children}</>;
}

// Hook：检查权限
export function useHelpRequestPermission() {
  const { isAuthenticated, user, isAdmin } = useAuth();

  const checkPermission = (action: PermissionAction, helpRequest?: HelpRequest): boolean => {
    // 查看权限：所有人都可以
    if (action === 'VIEW') {
      return true;
    }

    // 需要登录的操作
    if (!isAuthenticated) {
      return false;
    }

    // 采纳答案：只有求助者可以
    if (action === 'ADOPT' && helpRequest) {
      return user?.id === helpRequest.requester.id;
    }

    // 删除权限：管理员或求助者
    if (action === 'DELETE' && helpRequest) {
      return isAdmin() || user?.id === helpRequest.requester.id;
    }

    // 管理权限：只有管理员
    if (action === 'MODERATE') {
      return isAdmin();
    }

    // 其他需要登录的操作
    return isAuthenticated;
  };

  return { checkPermission };
}

// 权限按钮组件
interface PermissionButtonProps {
  action: PermissionAction;
  helpRequest?: HelpRequest;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  showTooltip?: boolean;
}

export function PermissionButton({
  action,
  helpRequest,
  onClick,
  children,
  className = "",
  disabled = false,
  showTooltip = true,
}: PermissionButtonProps) {
  const { checkPermission } = useHelpRequestPermission();
  const hasPermission = checkPermission(action, helpRequest);

  if (!hasPermission) {
    if (showTooltip) {
      const tooltips = {
        VIEW: "无权限查看",
        CREATE: "请登录后发布求助",
        ANSWER: "请登录后回答求助",
        ADOPT: "只有求助者可以采纳答案",
        DELETE: "只有管理员或求助者可以删除",
        MODERATE: "只有管理员可以管理求助",
      };

      return (
        <button
          disabled
          className={`${className} opacity-50 cursor-not-allowed`}
          title={tooltips[action]}
        >
          {children}
        </button>
      );
    }
    return null;
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
    >
      {children}
    </button>
  );
}

// 权限链接组件
interface PermissionLinkProps {
  action: PermissionAction;
  helpRequest?: HelpRequest;
  href: string;
  children: React.ReactNode;
  className?: string;
  showTooltip?: boolean;
}

export function PermissionLink({
  action,
  helpRequest,
  href,
  children,
  className = "",
  showTooltip = true,
}: PermissionLinkProps) {
  const { checkPermission } = useHelpRequestPermission();
  const hasPermission = checkPermission(action, helpRequest);

  if (!hasPermission) {
    if (showTooltip) {
      const tooltips = {
        VIEW: "无权限查看",
        CREATE: "请登录后发布求助",
        ANSWER: "请登录后回答求助",
        ADOPT: "只有求助者可以采纳答案",
        DELETE: "只有管理员或求助者可以删除",
        MODERATE: "只有管理员可以管理求助",
      };

      return (
        <span
          className={`${className} opacity-50 cursor-not-allowed`}
          title={tooltips[action]}
        >
          {children}
        </span>
      );
    }
    return null;
  }

  return (
    <Link href={href} className={className}>
      {children}
    </Link>
  );
}
