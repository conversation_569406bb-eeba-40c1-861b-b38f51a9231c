"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { verifyEmail, resendVerificationEmail } from "@/services/authService";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import {
  Mail,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowLeft,
  RefreshCw,
  Send,
} from "lucide-react";

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [verifying, setVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<
    "pending" | "success" | "failed" | "expired"
  >("pending");
  const [showResendForm, setShowResendForm] = useState(false);
  const [resendEmail, setResendEmail] = useState("");
  const [resendLoading, setResendLoading] = useState(false);

  // 获取验证令牌
  useEffect(() => {
    const tokenParam = searchParams?.get("token");
    if (!tokenParam) {
      setError("验证链接无效或已过期");
      setVerificationStatus("failed");
      setLoading(false);
    } else {
      setToken(tokenParam);
      // 自动开始验证
      verifyEmailToken(tokenParam);
    }
  }, [searchParams]);

  const verifyEmailToken = async (verificationToken: string) => {
    setVerifying(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await verifyEmail({ token: verificationToken });

      if (result.success) {
        setSuccess(result.message || "邮箱验证成功");
        setVerificationStatus("success");

        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push("/auth/login?message=email_verified");
        }, 3000);
      } else {
        // 根据错误类型设置不同的状态
        if (
          result.message?.includes("过期") ||
          result.message?.includes("expired")
        ) {
          setVerificationStatus("expired");
        } else {
          setVerificationStatus("failed");
        }
        setError(result.message || "邮箱验证失败");
      }
    } catch {
      setError("网络错误，请稍后重试");
      setVerificationStatus("failed");
    } finally {
      setLoading(false);
      setVerifying(false);
    }
  };

  const handleRetryVerification = () => {
    if (token) {
      setLoading(true);
      setVerificationStatus("pending");
      verifyEmailToken(token);
    }
  };

  const handleResendEmail = async () => {
    if (!resendEmail.trim()) {
      setError("请输入邮箱地址");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(resendEmail)) {
      setError("请输入有效的邮箱地址");
      return;
    }

    setResendLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await resendVerificationEmail({ email: resendEmail });

      if (result.success) {
        setSuccess(result.message || "验证邮件已重新发送");
        setShowResendForm(false);
        setResendEmail("");
      } else {
        setError(result.message || "发送失败，请稍后重试");
      }
    } catch {
      setError("网络错误，请稍后重试");
    } finally {
      setResendLoading(false);
    }
  };

  // 加载状态
  if (loading || verifying) {
    return (
      <PageContainer>
        <div className="min-h-[80vh] flex items-center justify-center py-12">
          <div className="w-full max-w-md">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Loader2 className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
                  <h2 className="text-xl font-semibold mb-2">正在验证邮箱</h2>
                  <p className="text-muted-foreground">
                    请稍候，我们正在验证您的邮箱地址...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  // 成功状态
  if (verificationStatus === "success") {
    return (
      <PageContainer>
        <div className="min-h-[80vh] flex items-center justify-center py-12">
          <div className="w-full max-w-md">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h2 className="text-xl font-semibold mb-2">邮箱验证成功</h2>
                  <p className="text-muted-foreground mb-6">
                    您的邮箱已成功验证，现在可以正常使用所有功能了。
                  </p>

                  {success && (
                    <Alert variant="success" className="mb-6">
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        <div>
                          <p>{success}</p>
                          <p className="text-xs mt-1 opacity-80">
                            3秒后自动跳转到登录页面...
                          </p>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  <Button asChild className="w-full">
                    <Link href="/auth/login">立即登录</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  // 错误状态
  return (
    <PageContainer>
      <div className="min-h-[80vh] flex items-center justify-center py-12">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-display">邮箱验证</CardTitle>
              <CardDescription>
                {verificationStatus === "expired"
                  ? "验证链接已过期，请重新发送验证邮件"
                  : "验证您的邮箱地址以完成注册"}
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-semibold mb-2">
                  {verificationStatus === "expired" ? "链接已过期" : "验证失败"}
                </h3>

                {/* 错误提示 */}
                {error && (
                  <Alert variant="destructive" className="mb-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* 成功提示 */}
                {success && (
                  <Alert variant="success" className="mb-6">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}

                {/* 重新发送邮件表单 */}
                {showResendForm ? (
                  <div className="space-y-4 mb-6">
                    <div className="text-left">
                      <Label htmlFor="resend-email">邮箱地址</Label>
                      <Input
                        id="resend-email"
                        type="email"
                        placeholder="请输入您的邮箱地址"
                        value={resendEmail}
                        onChange={(e) => setResendEmail(e.target.value)}
                        disabled={resendLoading}
                      />
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        onClick={handleResendEmail}
                        disabled={resendLoading}
                        className="flex-1"
                      >
                        {resendLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            发送中...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            发送
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowResendForm(false);
                          setResendEmail("");
                          setError(null);
                        }}
                        disabled={resendLoading}
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {token && verificationStatus !== "expired" && (
                      <Button
                        onClick={handleRetryVerification}
                        disabled={verifying}
                        className="w-full"
                      >
                        {verifying ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            重新验证中...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            重新验证
                          </>
                        )}
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      onClick={() => setShowResendForm(true)}
                      className="w-full"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      重新发送验证邮件
                    </Button>

                    <Button variant="outline" asChild className="w-full">
                      <Link href="/auth/login">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        返回登录
                      </Link>
                    </Button>
                  </div>
                )}

                <div className="mt-6 text-sm text-muted-foreground">
                  <p>
                    {verificationStatus === "expired"
                      ? "验证链接通常在24小时后过期，请重新发送验证邮件。"
                      : "如果您继续遇到问题，请联系客服获取帮助。"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
}
