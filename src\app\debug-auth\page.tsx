"use client";

import React, { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { isAuthenticated, getCurrentUser } from "@/services/authService";
import PersonalMenu from "@/components/PersonalMenu";

export default function DebugAuthPage() {
  const { user, isAuthenticated: hookIsAuth, isLoading } = useAuth();
  const [serviceAuth, setServiceAuth] = useState<boolean>(false);
  const [serviceUser, setServiceUser] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);
  const [authUser, setAuthUser] = useState<string | null>(null);
  const [expires, setExpires] = useState<string | null>(null);
  const [stateHistory, setStateHistory] = useState<any[]>([]);

  // 记录状态变化历史
  useEffect(() => {
    const newState = {
      timestamp: new Date().toISOString(),
      hookIsAuth,
      user: user?.username,
      isLoading,
      serviceAuth,
      hasToken: !!token,
    };

    setStateHistory((prev) => [...prev.slice(-9), newState]); // 保留最近10条记录
  }, [hookIsAuth, user, isLoading, serviceAuth, token]);

  useEffect(() => {
    // 检查服务层的认证状态
    const checkServiceAuth = async () => {
      const authStatus = isAuthenticated();
      setServiceAuth(authStatus);

      if (authStatus) {
        try {
          const user = await getCurrentUser();
          setServiceUser(user);
        } catch (error) {
          console.error("获取用户信息失败:", error);
        }
      }

      // 检查 localStorage 中的所有认证相关数据
      const storedToken = localStorage.getItem("auth_token");
      const storedUser = localStorage.getItem("auth_user");
      const storedExpires = localStorage.getItem("auth_expires");

      setToken(storedToken);
      setAuthUser(storedUser);
      setExpires(storedExpires);
    };

    checkServiceAuth();

    // 每秒检查一次状态变化
    const interval = setInterval(checkServiceAuth, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">认证状态调试页面</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* useAuth Hook 状态 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">useAuth Hook 状态</h2>
          <div className="space-y-2">
            <p>
              <strong>isLoading:</strong> {isLoading ? "true" : "false"}
            </p>
            <p>
              <strong>isAuthenticated:</strong> {hookIsAuth ? "true" : "false"}
            </p>
            <p>
              <strong>user:</strong>{" "}
              {user ? JSON.stringify(user, null, 2) : "null"}
            </p>
          </div>
        </div>

        {/* 服务层状态 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">服务层状态</h2>
          <div className="space-y-2">
            <p>
              <strong>isAuthenticated():</strong>{" "}
              {serviceAuth ? "true" : "false"}
            </p>
            <p>
              <strong>getCurrentUser():</strong>{" "}
              {serviceUser ? JSON.stringify(serviceUser, null, 2) : "null"}
            </p>
            <p>
              <strong>localStorage token:</strong> {token ? "存在" : "不存在"}
            </p>
            <p>
              <strong>localStorage user:</strong> {authUser ? "存在" : "不存在"}
            </p>
            <p>
              <strong>token expires:</strong> {expires || "无"}
            </p>
          </div>
        </div>

        {/* 状态变化历史 */}
        <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
          <h2 className="text-lg font-semibold mb-4">状态变化历史</h2>
          <div className="max-h-64 overflow-y-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">时间</th>
                  <th className="text-left p-2">Hook认证</th>
                  <th className="text-left p-2">用户</th>
                  <th className="text-left p-2">加载中</th>
                  <th className="text-left p-2">服务认证</th>
                  <th className="text-left p-2">有Token</th>
                </tr>
              </thead>
              <tbody>
                {stateHistory.map((state, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2">
                      {new Date(state.timestamp).toLocaleTimeString()}
                    </td>
                    <td className="p-2">{state.hookIsAuth ? "✅" : "❌"}</td>
                    <td className="p-2">{state.user || "无"}</td>
                    <td className="p-2">{state.isLoading ? "⏳" : "✅"}</td>
                    <td className="p-2">{state.serviceAuth ? "✅" : "❌"}</td>
                    <td className="p-2">{state.hasToken ? "✅" : "❌"}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* PersonalMenu 组件测试 */}
        <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
          <h2 className="text-lg font-semibold mb-4">PersonalMenu 组件</h2>
          <div className="flex justify-end">
            <PersonalMenu />
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mt-6 space-x-4">
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          刷新页面
        </button>
        <button
          type="button"
          onClick={() => {
            localStorage.clear();
            window.location.reload();
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          清除 localStorage
        </button>
      </div>
    </div>
  );
}
