"use client";

import React, { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { isAuthenticated, getCurrentUser } from "@/services/authService";
import PersonalMenu from "@/components/PersonalMenu";

export default function DebugAuthPage() {
  const { user, isAuthenticated: hookIsAuth, isLoading } = useAuth();
  const [serviceAuth, setServiceAuth] = useState<boolean>(false);
  const [serviceUser, setServiceUser] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    // 检查服务层的认证状态
    const checkServiceAuth = async () => {
      const authStatus = isAuthenticated();
      setServiceAuth(authStatus);
      
      if (authStatus) {
        try {
          const user = await getCurrentUser();
          setServiceUser(user);
        } catch (error) {
          console.error("获取用户信息失败:", error);
        }
      }
      
      // 检查 localStorage 中的 token
      const storedToken = localStorage.getItem("auth_token");
      setToken(storedToken);
    };

    checkServiceAuth();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">认证状态调试页面</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* useAuth Hook 状态 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">useAuth Hook 状态</h2>
          <div className="space-y-2">
            <p><strong>isLoading:</strong> {isLoading ? "true" : "false"}</p>
            <p><strong>isAuthenticated:</strong> {hookIsAuth ? "true" : "false"}</p>
            <p><strong>user:</strong> {user ? JSON.stringify(user, null, 2) : "null"}</p>
          </div>
        </div>

        {/* 服务层状态 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">服务层状态</h2>
          <div className="space-y-2">
            <p><strong>isAuthenticated():</strong> {serviceAuth ? "true" : "false"}</p>
            <p><strong>getCurrentUser():</strong> {serviceUser ? JSON.stringify(serviceUser, null, 2) : "null"}</p>
            <p><strong>localStorage token:</strong> {token ? "存在" : "不存在"}</p>
          </div>
        </div>

        {/* PersonalMenu 组件测试 */}
        <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
          <h2 className="text-lg font-semibold mb-4">PersonalMenu 组件</h2>
          <div className="flex justify-end">
            <PersonalMenu />
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mt-6 space-x-4">
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          刷新页面
        </button>
        <button
          onClick={() => {
            localStorage.clear();
            window.location.reload();
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          清除 localStorage
        </button>
      </div>
    </div>
  );
}
