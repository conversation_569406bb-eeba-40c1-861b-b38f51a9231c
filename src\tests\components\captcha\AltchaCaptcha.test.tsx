/**
 * ALTCHA验证码组件测试
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AltchaCaptcha, useAltchaCaptcha } from '@/components/captcha/AltchaCaptcha';

// Mock ALTCHA module
jest.mock('altcha', () => ({}));

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('AltchaCaptcha Component', () => {
  const mockOnVerified = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    render(
      <AltchaCaptcha 
        onVerified={mockOnVerified}
        onError={mockOnError}
      />
    );

    expect(screen.getByText('加载验证组件...')).toBeInTheDocument();
    expect(screen.getByText('人机验证')).toBeInTheDocument();
  });

  test('shows error message when provided', () => {
    render(
      <AltchaCaptcha 
        onVerified={mockOnVerified}
        onError={mockOnError}
      />
    );

    // Simulate error state by checking if error display works
    expect(screen.getByText('人机验证')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    const { container } = render(
      <AltchaCaptcha 
        onVerified={mockOnVerified}
        onError={mockOnError}
        className="custom-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  test('disables component when disabled prop is true', () => {
    render(
      <AltchaCaptcha 
        onVerified={mockOnVerified}
        onError={mockOnError}
        disabled={true}
      />
    );

    const altchaWidget = document.querySelector('altcha-widget');
    expect(altchaWidget).toHaveStyle('opacity: 0.5');
    expect(altchaWidget).toHaveStyle('pointer-events: none');
  });
});

describe('useAltchaCaptcha Hook', () => {
  const TestComponent = () => {
    const {
      captchaPayload,
      isVerified,
      handleVerified,
      handleError,
      reset
    } = useAltchaCaptcha();

    return (
      <div>
        <span data-testid="payload">{captchaPayload || 'null'}</span>
        <span data-testid="verified">{isVerified ? 'true' : 'false'}</span>
        <button onClick={() => handleVerified('test-payload')}>
          Verify
        </button>
        <button onClick={handleError}>Error</button>
        <button onClick={reset}>Reset</button>
      </div>
    );
  };

  test('initial state is correct', () => {
    render(<TestComponent />);

    expect(screen.getByTestId('payload')).toHaveTextContent('null');
    expect(screen.getByTestId('verified')).toHaveTextContent('false');
  });

  test('handleVerified updates state correctly', async () => {
    render(<TestComponent />);

    fireEvent.click(screen.getByText('Verify'));

    await waitFor(() => {
      expect(screen.getByTestId('payload')).toHaveTextContent('test-payload');
      expect(screen.getByTestId('verified')).toHaveTextContent('true');
    });
  });

  test('handleError resets verification state', async () => {
    render(<TestComponent />);

    // First verify
    fireEvent.click(screen.getByText('Verify'));
    
    await waitFor(() => {
      expect(screen.getByTestId('verified')).toHaveTextContent('true');
    });

    // Then trigger error
    fireEvent.click(screen.getByText('Error'));

    await waitFor(() => {
      expect(screen.getByTestId('payload')).toHaveTextContent('null');
      expect(screen.getByTestId('verified')).toHaveTextContent('false');
    });
  });

  test('reset function works correctly', async () => {
    render(<TestComponent />);

    // First verify
    fireEvent.click(screen.getByText('Verify'));
    
    await waitFor(() => {
      expect(screen.getByTestId('verified')).toHaveTextContent('true');
    });

    // Then reset
    fireEvent.click(screen.getByText('Reset'));

    await waitFor(() => {
      expect(screen.getByTestId('payload')).toHaveTextContent('null');
      expect(screen.getByTestId('verified')).toHaveTextContent('false');
    });
  });
});