"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import { getProfileStatistics, getActivitySummary } from "@/services/profileService";
import { getCurrentUser } from "@/services/authService";
import type { ActivitySummary } from "@/services/profileService";
import { ProfileCard } from "@/components/profile/ProfileCard";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/Button";

import { PageContainer } from "@/components/layout/PageContainer";
import AuthGuard from "@/components/AuthGuard";
import {
  User,
  Settings,
  Award,
  MessageSquare,
  HelpCircle,
  TrendingUp,
  Calendar,
} from "lucide-react";

interface ProfileStats {
  total_help_requests: number;
  total_help_answers: number;
  accepted_answers: number;
  total_points: number;
  current_title: string;
}

// 未登录状态组件
const UnauthorizedFallback = () => {
  const router = useRouter();

  useEffect(() => {
    // 延迟3.5秒后跳转到登录界面
    const timer = setTimeout(() => {
      router.push("/login?redirect=" + encodeURIComponent("/profile"));
    }, 3500);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <PageContainer>
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mb-6">
            <User className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              未登录
            </h2>
            <p className="text-muted-foreground mb-4">
              您需要登录后才能查看个人中心
            </p>
            <p className="text-sm text-muted-foreground">
              3.5秒后将自动跳转到登录页面...
            </p>
          </div>

          <Button
            onClick={() =>
              router.push("/login?redirect=" + encodeURIComponent("/profile"))
            }
            className="w-full"
          >
            立即登录
          </Button>
        </div>
      </div>
    </PageContainer>
  );
};

export default function ProfilePage() {
  const router = useRouter();
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [activitySummary, setActivitySummary] = useState<ActivitySummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    setLoading(true);

    try {
      // 并行获取统计数据和活动摘要
      const [statsResult, activityResult] = await Promise.allSettled([
        getProfileStatistics(),
        getActivitySummary()
      ]);

      // 处理统计数据
      if (statsResult.status === 'fulfilled' &&
          statsResult.value.success &&
          statsResult.value.data &&
          statsResult.value.data.total_points !== undefined) {
        // 如果统计API返回了有效数据，使用统计数据
        setStats(statsResult.value.data);
      } else {
        // 否则使用用户基本信息中的积分数据作为备用
        const userInfo = await getCurrentUser();

        if (userInfo) {
          const fallbackStats: ProfileStats = {
            total_points: userInfo.points || 0,
            current_title: userInfo.title || "资源拾荒者",
            total_help_requests: 0,
            total_help_answers: 0,
            accepted_answers: 0,
          };
          setStats(fallbackStats);
        }
      }

      // 处理活动摘要数据
      if (activityResult.status === 'fulfilled' &&
          activityResult.value.success &&
          activityResult.value.data) {
        setActivitySummary(activityResult.value.data);
      } else {
        console.log("获取活动摘要失败:", activityResult.status === 'fulfilled' ? activityResult.value : activityResult.reason);
      }
    } catch (error) {
      console.error("加载个人资料数据失败:", error);
      // 即使出错也尝试获取基本的用户信息
      try {
        const userInfo = await getCurrentUser();
        if (userInfo) {
          const fallbackStats: ProfileStats = {
            total_points: userInfo.points || 0,
            current_title: userInfo.title || "资源拾荒者",
            total_help_requests: 0,
            total_help_answers: 0,
            accepted_answers: 0,
          };
          setStats(fallbackStats);
        }
      } catch (userError) {
        console.error("获取用户信息失败:", userError);
      }
    }

    setLoading(false);
  };

  const StatCard = ({
    title,
    value,
    icon: Icon,
    description,
    onClick,
  }: {
    title: string;
    value: string | number;
    icon: any;
    description?: string;
    onClick?: () => void;
  }) => (
    <Card
      className={`${
        onClick ? "cursor-pointer hover:shadow-md transition-shadow" : ""
      }`}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="min-h-[60vh] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard requireAuth={true} fallback={<UnauthorizedFallback />}>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-display font-bold">个人资料</h1>
            <p className="text-muted-foreground mt-2">查看和管理您的个人信息</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：个人信息卡片 */}
            <div className="lg:col-span-2">
              <ProfileCard />
            </div>

            {/* 右侧：快捷操作 */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">快捷操作</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/edit")}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    编辑资料
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/points")}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    积分历史
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/help-requests")}
                  >
                    <HelpCircle className="h-4 w-4 mr-2" />
                    我的求助
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/help-answers")}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    我的回答
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* 统计信息 */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold mb-4">活动统计</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="总积分"
                  value={stats.total_points ?? 0}
                  icon={Award}
                  description={`当前等级：${
                    stats.current_title ?? "资源拾荒者"
                  }`}
                  onClick={() => router.push("/profile/points")}
                />

                <StatCard
                  title="发起求助"
                  value={stats.total_help_requests ?? 0}
                  icon={HelpCircle}
                  description="累计发起的求助"
                  onClick={() => router.push("/profile/help-requests")}
                />

                <StatCard
                  title="提供回答"
                  value={stats.total_help_answers ?? 0}
                  icon={MessageSquare}
                  description="累计提供的回答"
                  onClick={() => router.push("/profile/help-answers")}
                />

                <StatCard
                  title="被采纳回答"
                  value={stats.accepted_answers ?? 0}
                  icon={TrendingUp}
                  description={`采纳率：${
                    (stats.total_help_answers ?? 0) > 0
                      ? Math.round(
                          ((stats.accepted_answers ?? 0) /
                            (stats.total_help_answers ?? 1)) *
                            100
                        )
                      : 0
                  }%`}
                />
              </div>
            </div>
          )}

          {/* 最近活动 */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  最近活动
                </CardTitle>
                <CardDescription>查看您最近的活动记录</CardDescription>
              </CardHeader>
              <CardContent>
                {activitySummary ? (
                  <div className="space-y-6">
                    {/* 最近求助 */}
                    {activitySummary.recent_help_requests && activitySummary.recent_help_requests.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-3">最近发起的求助</h4>
                        <div className="space-y-2">
                          {activitySummary.recent_help_requests.map((request) => (
                            <div key={request.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 cursor-pointer" 
                                 onClick={() => router.push(`/help-requests/${request.id}`)}>
                              <div className="flex items-center space-x-3">
                                <HelpCircle className="h-4 w-4 text-blue-500" />
                                <div>
                                  <p className="font-medium text-sm">{request.title}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {new Date(request.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                request.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                request.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                              }`}>
                                {request.status === 'resolved' ? '已解决' : request.status === 'pending' ? '待解决' : request.status}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 最近积分交易 */}
                    {activitySummary.recent_points_transactions && activitySummary.recent_points_transactions.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-3">最近积分变动</h4>
                        <div className="space-y-2">
                          {activitySummary.recent_points_transactions.slice(0, 5).map((transaction) => (
                            <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center space-x-3">
                                <Award className={`h-4 w-4 ${transaction.amount > 0 ? 'text-green-500' : 'text-red-500'}`} />
                                <div>
                                  <p className="font-medium text-sm">{transaction.description}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {new Date(transaction.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                                transaction.amount > 0 
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              }`}>
                                {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 没有活动数据的情况 */}
                    {(!activitySummary.recent_help_requests || activitySummary.recent_help_requests.length === 0) &&
                     (!activitySummary.recent_points_transactions || activitySummary.recent_points_transactions.length === 0) && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>暂无最近活动记录</p>
                        <p className="text-sm mt-1">开始使用平台功能来查看活动记录</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>加载活动记录中...</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
