import Link from "next/link";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function Loading() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-4 md:py-6">
      {/* Loading状态的求助详情卡片 */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl mb-6 shadow-sm animate-pulse">
        <div className="p-4 md:p-6">
          {/* 标题加载状态 */}
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4 w-3/4"></div>

          {/* 标签行加载状态 */}
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-16"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
          </div>

          {/* 用户信息加载状态 */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex flex-col">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-1 w-20"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            </div>
          </div>

          {/* 描述加载状态 */}
          <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-full"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-5/6"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      </div>

      {/* 回答列表加载状态 */}
      <div className="space-y-4">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-40 px-1"></div>
        
        {/* 回答卡片加载状态 */}
        {[1, 2].map((index) => (
          <div 
            key={index}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-sm animate-pulse"
          >
            <div className="p-4">
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded mb-3 w-4/5"></div>
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full mb-4 w-16"></div>
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                  <div className="flex flex-col">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-1 w-16"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  </div>
                </div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-3 border border-gray-200 dark:border-gray-700 mb-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 返回链接 */}
      <div className="mt-6 text-center">
        <Link
          href="/help-requests"
          className="inline-flex items-center px-4 py-2 text-blue-600 hover:text-blue-700 transition-colors"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          返回求助列表
        </Link>
      </div>
    </div>
  );
}