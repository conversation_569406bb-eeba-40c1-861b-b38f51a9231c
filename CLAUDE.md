# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management & Development
```bash
npm install              # Install dependencies
npm run dev             # Start development server on localhost:3000
npm run build           # Build for production
npm start              # Start production server
```

### Code Quality & Testing
```bash
npm run lint            # Run ESLint for code linting
npm run test           # Run tests with Vitest
npm run test:ui        # Run tests with Vitest UI
npm run test:run       # Run tests once (CI mode)
```

## Project Architecture

### Technology Stack
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript with strict type checking
- **Styling**: Tailwind CSS 4
- **State Management**: Zustand for global state
- **Testing**: Vitest with @testing-library
- **API Client**: Custom fetch wrapper with automatic token refresh
- **UI Components**: Radix UI primitives with custom styling

### Core Architecture Patterns

#### Service Layer Pattern
All API interactions are centralized in the `src/services/` directory:
- `api.ts` - Generic API client with automatic token refresh and error handling
- `authService.ts` - Authentication and user management
- `resourceService.ts` - Resource search and management
- `profileService.ts` - User profile operations
- `helpRequestService.ts` - Community help system

#### Hook-Based State Management
Custom hooks in `src/hooks/` provide reusable stateful logic:
- `useAuth.ts` - Complete authentication state with permission checking
- `useSearch.ts` - Search functionality with caching and filters
- `useCloudLinkHandler.ts` - Network drive link processing
- Authentication state syncs across browser tabs via localStorage events

#### Error Handling & Performance
- `ChunkErrorBoundary` - Handles dynamic import failures
- `ErrorHandler` - Global error boundary with user-friendly messages  
- Token refresh system with automatic retry on 401 responses
- Resource preloading and caching for improved performance

### Key Directories Structure

#### `/src/app/` - Next.js App Router Pages
- Page components use `page.tsx` convention
- Layouts use `layout.tsx` for shared UI
- API routes in `/api/` subdirectories
- Client-side pages often have corresponding server components

#### `/src/components/` - React Components
- **UI Components**: `/ui/` contains reusable Radix-based components
- **Feature Components**: Domain-specific components (ResourceCard, Navigation, etc.)
- **Layout Components**: Page structure components (PageContainer, ConditionalLayout)
- Components use TypeScript interfaces for props and follow PascalCase naming

#### `/src/types/` - TypeScript Definitions
Shared type definitions for:
- `resource.ts` - Resource data structures and search types
- `help-request.ts` - Community help system types  
- `admin.ts` - Administrative interfaces
- `user-level.ts` - User permission and level definitions

### Authentication & Authorization System

The app uses a role-based permission system:
- **Roles**: `user`, `moderator`, `admin` with hierarchical permissions
- **Token Management**: JWT with automatic refresh and multi-tab sync
- **Permission Hooks**: `useAuth().hasPermission()`, `isAdmin()`, `requireAuth()`
- **Route Protection**: AuthGuard components and middleware

### API Integration Patterns

#### Standardized API Responses
```typescript
interface ApiResponse<T> {
  status: string;
  data?: T;
  message?: string;
  error?: string;
}
```

#### API Client Features
- Automatic bearer token attachment
- 401 error handling with token refresh retry
- Environment-based API base URL configuration
- Request/response interceptors for consistent error handling

### Link Processing System

The platform handles multiple cloud storage providers with configurable link processing:
- Environment variables control `get_share` API usage per provider type
- Fallback mechanisms for different cloud drive link formats
- Resource status checking and link validation

## Development Guidelines

### Code Style
- Use Chinese comments for better team collaboration
- Follow TypeScript strict mode requirements
- Use Tailwind CSS for all styling (prefer utility classes)
- Component files and components use PascalCase naming

### State Management
- Use Zustand for global state (loading states, etc.)
- Prefer custom hooks for component-level state logic
- Implement proper hydration handling to avoid SSR mismatches
- Use React Server Components where appropriate

### API Development  
- All API routes follow `/api/` convention in App Router
- Implement proper error responses with consistent format
- Use middleware for authentication and request validation
- Handle CORS issues via Next.config.js rewrites when needed

### Testing Strategy
- Unit tests for utilities and pure functions
- Component tests using React Testing Library
- Integration tests for complex user flows
- Mock API responses for consistent testing

## Environment Configuration

### Required Environment Variables
```bash
NEXT_PUBLIC_API_BASE_URL          # Backend API URL
API_PROXY_TARGET                  # Development proxy target
NEXT_PUBLIC_ENABLE_GET_SHARE_API  # Enable share link API (default: true)
NEXT_PUBLIC_GET_SHARE_TYPES       # Comma-separated cloud types for share API
```

### Development Setup
1. The app proxies `/api/*` requests to backend via Next.config.js rewrites
2. Default proxy target is `http://127.0.0.1:9999` 
3. Webpack configuration optimizes chunk loading to prevent errors
4. Hot reload and fast refresh are configured for optimal development experience

## Known Issues & Solutions

### Chunk Loading Errors
- Custom webpack configuration in `next.config.js` reduces chunk splitting
- `ChunkErrorBoundary` provides graceful fallback UI
- Service worker configuration helps with chunk caching

### Hydration Mismatches  
- Authentication state initialized conservatively to match SSR
- `isMounted` flags prevent hydration conflicts
- Theme provider properly handles system theme detection

### Multi-tab Authentication Sync
- localStorage event listeners sync auth state across tabs
- Token refresh coordinated to prevent race conditions
- Proper cleanup prevents memory leaks

## Resources and Documentation

### Internal Documentation
- Chinese README.md provides comprehensive setup and feature documentation
- `/docs/` directory contains detailed feature implementation guides
- Cursor rules file (`.cursor/rules/pan-so-fronted.mdc`) defines coding standards

### Key External Dependencies
- **Next.js**: App Router, Server Components, API routes
- **React Hook Form + Zod**: Form validation and type safety
- **Fuse.js**: Client-side fuzzy search capabilities
- **React Markdown**: Tutorial and content rendering
- **Axios**: HTTP client for some legacy API calls